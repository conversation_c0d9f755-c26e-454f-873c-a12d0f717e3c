<?php

namespace App\Traits;

use App\Enums\Loan\LoanTxnStatus;
use App\Enums\Loan\LoanTxnType;
use App\Models\LoanTxn;
use App\Models\LoanTxnDetail;
use App\Models\LoanTxnType as LoanTxnTypeModel;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

/**
 * Trait GeneratesLoanTransactions
 *
 * This trait provides methods to generate loan transactions and transaction details.
 * It can be used by models that need to create loan transactions, reducing code duplication.
 *
 * Usage examples:
 *
 * 1. Generate installment transactions (used by HasLoanInstallments trait):
 *    $this->generateInstallmentTransactions($installments, $loanId);
 *
 * 2. Generate a single transaction with details:
 *    $transaction = $this->generateLoanTransaction([
 *        'loan_id' => 1,
 *        'loan_installment_id' => 1,
 *        'loan_txn_type_id' => LoanTxnType::LATE_INTEREST->value,
 *        'tenure' => 1,
 *        'amount' => 100.00,
 *        'status' => LoanTxnStatus::UNPAID,
 *        'remark' => 'Late interest charge',
 *    ], [
 *        [
 *            'txn_type_id' => LoanTxnType::LATE_INTEREST->value,
 *            'txn_date' => now(),
 *            'amount' => 100.00,
 *            'precision_amount' => 100.00,
 *            'remark' => 'Late interest charge',
 *        ]
 *    ]);
 *
 * 3. Generate specific transaction types:
 *    $transaction = $this->generateLateInterestTransaction($loanId, $tenure, $installmentId, 50.00);
 *    $transaction = $this->generateLegalFeeTransaction($loanId, $tenure, $installmentId, 25.00);
 *    $transaction = $this->generateMiscChargeTransaction($loanId, $tenure, $installmentId, 10.00);
 *    $transaction = $this->generatePostageTransaction($loanId, $tenure, $installmentId, 5.00);
 */
trait GeneratesLoanTransactions
{
    /**
     * Generate loan transactions for installments
     * This method creates loan transactions and transaction details for each installment
     *
     * @param  array  $installments  Array of installment data
     * @param  int  $loanId  The loan ID
     * @return array Array of created transaction IDs
     *
     * @throws \RuntimeException
     */
    public function generateInstallmentTransactions(array $installments, int $loanId): array
    {
        try {
            $transactionIds = [];

            DB::transaction(function () use ($installments, $loanId, &$transactionIds) {
                foreach ($installments as $installment) {
                    $transaction = $this->createLoanTransaction([
                        'loan_id' => $loanId,
                        'loan_installment_id' => $installment['id'] ?? null,
                        'loan_txn_type_id' => LoanTxnType::INSTALLMENT->value,
                        'tenure' => $installment['tenure'] ?? null,
                        'amount' => $installment['total_amount'],
                        'status' => LoanTxnStatus::UNPAID,
                        'remark' => 'Installment transaction for tenure '.($installment['tenure'] ?? ''),
                    ]);

                    $transactionIds[] = $transaction->id;

                    $this->createTransactionDetails($transaction, [
                        [
                            'txn_type_id' => LoanTxnType::INSTALLMENT->value,
                            'txn_date' => $installment['pay_date'] ?? now(),
                            'amount' => $installment['principle_amount'] ?? 0,
                            'precision_amount' => $installment['principle_amount'] ?? 0,
                            'remark' => 'Principal amount for tenure '.($installment['tenure'] ?? ''),
                        ],
                        [
                            'txn_type_id' => LoanTxnType::INSTALLMENT->value,
                            'txn_date' => $installment['pay_date'] ?? now(),
                            'amount' => $installment['interest_amount_net'] ?? 0,
                            'precision_amount' => $installment['interest_amount_net'] ?? 0,
                            'remark' => 'Interest amount for tenure '.($installment['tenure'] ?? ''),
                        ],
                    ]);
                }
            });

            return $transactionIds;
        } catch (\Throwable $e) {
            Log::error('Installment transaction generation failed', [
                'loan_id' => $loanId,
                'error' => $e->getMessage(),
            ]);

            throw new \RuntimeException('Failed to generate installment transactions.', 0, $e);
        }
    }

    /**
     * Generate a single loan transaction
     *
     * @param  array  $transactionData  Transaction data
     * @param  array  $detailsData  Array of transaction details data
     * @return LoanTxn Created transaction
     *
     * @throws \RuntimeException
     */
    public function generateLoanTransaction(array $transactionData, array $detailsData = []): LoanTxn
    {
        try {
            return DB::transaction(function () use ($transactionData, $detailsData) {
                $transaction = $this->createLoanTransaction($transactionData);

                if (! empty($detailsData)) {
                    $this->createTransactionDetails($transaction, $detailsData);
                }

                return $transaction;
            });
        } catch (\Throwable $e) {
            Log::error('Loan transaction generation failed', [
                'transaction_data' => $transactionData,
                'error' => $e->getMessage(),
            ]);

            throw new \RuntimeException('Failed to generate loan transaction.', 0, $e);
        }
    }

    /**
     * Generate late interest transaction
     */
    public function generateLateInterestTransaction(int $loanId, int $installmentId, int $tenure, float $amount, ?string $remark = null): LoanTxn
    {
        return $this->generateLoanTransaction([
            'loan_id' => $loanId,
            'loan_installment_id' => $installmentId,
            'loan_txn_type_id' => LoanTxnType::LATE_INTEREST->value,
            'tenure' => $tenure,
            'amount' => $amount,
            'status' => LoanTxnStatus::UNPAID,
            'remark' => $remark ?? 'Late interest charge',
        ], [
            [
                'txn_type_id' => LoanTxnType::LATE_INTEREST->value,
                'txn_date' => now(),
                'amount' => $amount,
                'precision_amount' => $amount,
                'remark' => $remark ?? 'Late interest charge',
            ],
        ]);
    }

    /**
     * Generate legal fee transaction
     */
    public function generateLegalFeeTransaction(int $loanId, int $installmentId, int $tenure, float $amount, ?string $remark = null): LoanTxn
    {
        return $this->generateLoanTransaction([
            'loan_id' => $loanId,
            'loan_installment_id' => $installmentId,
            'loan_txn_type_id' => LoanTxnType::LEGAL_FEE->value,
            'tenure' => $tenure,
            'amount' => $amount,
            'status' => LoanTxnStatus::UNPAID,
            'remark' => $remark ?? 'Legal fee charge',
        ], [
            [
                'txn_type_id' => LoanTxnType::LEGAL_FEE->value,
                'txn_date' => now(),
                'amount' => $amount,
                'precision_amount' => $amount,
                'remark' => $remark ?? 'Legal fee charge',
            ],
        ]);
    }

    /**
     * Generate miscellaneous charge transaction
     */
    public function generateMiscChargeTransaction(int $loanId, int $installmentId, int $tenure, float $amount, ?string $remark = null): LoanTxn
    {
        return $this->generateLoanTransaction([
            'loan_id' => $loanId,
            'loan_installment_id' => $installmentId,
            'loan_txn_type_id' => LoanTxnType::MISC_CHARGE->value,
            'tenure' => $tenure,
            'amount' => $amount,
            'status' => LoanTxnStatus::UNPAID,
            'remark' => $remark ?? 'Miscellaneous charge',
        ], [
            [
                'txn_type_id' => LoanTxnType::MISC_CHARGE->value,
                'txn_date' => now(),
                'amount' => $amount,
                'precision_amount' => $amount,
                'remark' => $remark ?? 'Miscellaneous charge',
            ],
        ]);
    }

    /**
     * Generate postage transaction
     */
    public function generatePostageTransaction(int $loanId, int $installmentId, int $tenure, float $amount, ?string $remark = null): LoanTxn
    {
        return $this->generateLoanTransaction([
            'loan_id' => $loanId,
            'loan_installment_id' => $installmentId,
            'loan_txn_type_id' => LoanTxnType::POSTAGE->value,
            'tenure' => $tenure,
            'amount' => $amount,
            'status' => LoanTxnStatus::UNPAID,
            'remark' => $remark ?? 'Postage charge',
        ], [
            [
                'txn_type_id' => LoanTxnType::POSTAGE->value,
                'txn_date' => now(),
                'amount' => $amount,
                'precision_amount' => $amount,
                'remark' => $remark ?? 'Postage charge',
            ],
        ]);
    }

    /**
     * Create a loan transaction record
     *
     * @param  array  $data  Transaction data
     */
    protected function createLoanTransaction(array $data): LoanTxn
    {
        $txnType = null;
        if (isset($data['loan_txn_type_id'])) {
            $txnTypeModel = LoanTxnTypeModel::find($data['loan_txn_type_id']);
            $txnType = $txnTypeModel?->type?->label();
        }

        return LoanTxn::create([
            'uuid' => Str::uuid(),
            'loan_id' => $data['loan_id'],
            'loan_installment_id' => $data['loan_installment_id'] ?? null,
            'loan_txn_type_id' => $data['loan_txn_type_id'],
            'txn_date' => $data['txn_date'] ?? now(),
            'tenure' => $data['tenure'] ?? null,
            'txn_type' => $txnType,
            'amount' => $data['amount'],
            'status' => $data['status'] ?? LoanTxnStatus::UNPAID,
            'sort_date' => $data['sort_date'] ?? now(),
            'remark' => $data['remark'] ?? null,
            'created_by' => Auth::id(),
        ]);
    }

    /**
     * Create transaction detail records
     */
    protected function createTransactionDetails(LoanTxn $transaction, array $detailsData): void
    {
        foreach ($detailsData as $detail) {
            $txnType = null;
            if (isset($detail['txn_type_id'])) {
                $txnTypeModel = LoanTxnTypeModel::find($detail['txn_type_id']);
                $txnType = $txnTypeModel?->type?->label();
            }

            LoanTxnDetail::create([
                'uuid' => Str::uuid(),
                'loan_txn_id' => $transaction->id,
                'loan_txn_type_id' => $detail['txn_type_id'],
                'txn_type' => $txnType,
                'txn_date' => $detail['txn_date'] ?? now(),
                'amount' => $detail['amount'],
                'precision_amount' => $detail['precision_amount'] ?? $detail['amount'],
                'status' => $detail['status'] ?? LoanTxnStatus::UNPAID,
                'remark' => $detail['remark'] ?? null,
                'created_by' => Auth::id(),
            ]);
        }
    }
}
