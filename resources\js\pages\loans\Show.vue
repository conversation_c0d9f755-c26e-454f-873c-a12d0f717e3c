<script setup lang="ts">
import Collateral from '@/components/customer/Collateral.vue';
import FaIcon from '@/components/FaIcon.vue';
import FormShow from '@/components/form/FormShow.vue';
import Heading from '@/components/Heading.vue';
import TabsWrapper from '@/components/TabsWrapper.vue';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardFooter, CardHeader } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Ta<PERSON>, Ta<PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import AppLayout from '@/layouts/AppLayout.vue';
import { Head, router, useForm } from '@inertiajs/vue3';
import { computed, onMounted, reactive, ref } from 'vue';

interface Loan {
    id: number;
    uuid: string;
    code: string;
}

interface Props {
    loan: any;
    collateralTypes: Array<{
        id: number;
        value: string;
    }>;
    documentTypes: Array<{
        id: number;
        value: string;
    }>;
    transactionTypes: Array<{
        id: number;
        value: string;
    }>;
    letterTypes: Array<{
        id: number;
        value: string;
    }>;
}

interface Address {
    id: number;
    line_1: string | null;
    line_2: string | null;
    postcode: string | null;
    city: string | null;
    state: string | null;
    selection_state_id: number | null;
    state_selection: string | null;
    country: string | null;
    selection_country_id: number | null;
    country_selection: string | null;
}

const props = defineProps<Props>();
const form = useForm({});

const activeTab = ref('personal');
const ordinal = (n: number) => {
    const s = ['th', 'st', 'nd', 'rd'];
    const v = n % 100;
    return n + (s[(v - 20) % 10] || s[v] || s[0]);
};
const activeDocTab = ref('all');
const activeTransactionTab = ref('general');

const tabFlow = computed(() => ({
    personal: props.loan.type !== 'Collateral' ? 'loan' : 'loan',
    collateral: 'loan',
    loan: 'emergency',
    emergency: 'guarantor',
    guarantor: props.loan.status >= 5 ? 'bank' : 'document',
    bank: 'document',
    document: null,
}));

const tabBackFlow = computed(() => ({
    collateral: 'personal',
    loan: props.loan.type !== 'Collateral' ? 'personal' : 'collateral',
    emergency: 'loan',
    guarantor: 'emergency',
    document: props.loan.status >= 5 ? 'bank' : 'guarantor',
    bank: 'guarantor',
}));

const goToNextTab = () => {
    const current = activeTab.value as keyof typeof tabFlow.value;
    const next = tabFlow.value[current];
    if (next) activeTab.value = next;
};

const goToPreviousTab = () => {
    const current = activeTab.value as keyof typeof tabBackFlow.value;
    const prev = tabBackFlow.value[current];
    if (prev) activeTab.value = prev;
};

const tabItems = computed(() => {
    const items = [
        { label: 'Personal Details', value: 'personal' },
        { label: 'Collateral Details', value: 'collateral', disabled: props.loan.type !== 'Collateral' },
        { label: 'Loan Details', value: 'loan' },
        { label: 'Emergency Contact', value: 'emergency' },
        { label: 'Guarantor Contact', value: 'guarantor' },
        { label: 'Supporting Document', value: 'document' },
    ];

    if (props.loan.status >= 5) {
        const insertIndex = items.findIndex((item) => item.value === 'guarantor') + 1;
        items.splice(insertIndex, 0, { label: 'Bank Account Details', value: 'bank' });
    }

    return items;
});

const docItems = computed(() => [
    { label: 'All', value: 'all' },
    { label: 'Customer Documents', value: 'customer-doc' },
    { label: 'Collateral Documents', value: 'collateral-doc' },
    { label: 'Security Documents', value: 'security-doc' },
]);

const tabTransactionItems = computed(() => [
    { label: 'General', value: 'general' },
    { label: 'Transaction', value: 'transaction' },
    { label: 'Transaction Records', value: 'transaction-record' },
]);

const formatAddress = (address: Address | null) => {
    if (!address) return 'No address provided';

    const line1 = address.line_1;
    const line2 = address.line_2;
    const line3Parts = [address.postcode, address.city, address.state_selection || address.state, address.country_selection || address.country]
        .filter(Boolean)
        .join(', ');

    return [line1, line2, line3Parts].filter(Boolean).join('\n');
};

type FileData = {
    id?: number;
    name: string;
    size: number;
    url: string;
    typeId: number;
    type: string;
    createdAt: string;
    uploadedBy: string;
};

type TabKey = 'customer-doc' | 'collateral-doc' | 'security-doc' | 'all';
const categorizedFiles = reactive<Record<TabKey, FileData[]>>({
    'customer-doc': [],
    'collateral-doc': [],
    'security-doc': [],
    all: [],
});

const selectedDocIds = ref<(string | number | undefined)[]>([]);

const allDocs = computed(() => {
    return Object.values(categorizedFiles).flat();
});

const selectedFile = computed(() => {
    const id = selectedDocIds.value[0];
    return allDocs.value.find((doc) => doc.id === id) || null;
});

const toggleSelect = (index: number | undefined) => {
    if (selectedDocIds.value[0] === index) {
        selectedDocIds.value = [];
    } else {
        // Only allow one selected index at a time
        selectedDocIds.value = [index];
    }
};

const typeToTabKey: Record<number, TabKey> = {};
const CUSTOMER_DOC_ID = props.documentTypes.find((type) => type.value === 'Customer Documents')?.id ?? null;
const COLLATERAL_DOC_ID = props.documentTypes.find((type) => type.value === 'Collateral Documents')?.id ?? null;
const SECURITY_DOC_ID = props.documentTypes.find((type) => type.value === 'Security Documents')?.id ?? null;

if (CUSTOMER_DOC_ID) typeToTabKey[CUSTOMER_DOC_ID] = 'customer-doc';
if (COLLATERAL_DOC_ID) typeToTabKey[COLLATERAL_DOC_ID] = 'collateral-doc';
if (SECURITY_DOC_ID) typeToTabKey[SECURITY_DOC_ID] = 'security-doc';

onMounted(() => {
    if (props.loan.documents) {
        props.loan.documents.forEach((doc) => {
            const baseStorageUrl = `${window.location.origin}/storage/`;
            const rawPath = doc.file?.url || doc.url;
            const fullUrl = rawPath.startsWith('http') ? rawPath : baseStorageUrl + rawPath;

            const fileName = doc.file?.name || rawPath.split('/').pop();
            const extension = fileName?.split('.').pop()?.toLowerCase();

            let type = 'Other';
            if (extension === 'pdf') {
                type = 'PDF';
            } else if (['jpg', 'jpeg'].includes(extension)) {
                type = 'JPG';
            } else if (extension === 'png') {
                type = 'PNG';
            }

            const fileEntry = {
                id: doc.id,
                name: fileName,
                url: fullUrl,
                type,
                size: doc.file?.size,
                typeId: doc.selection_type_id,
                isExisting: true,
                createdAt: new Date(doc.created_at).toLocaleString(),
                uploadedBy: doc['uploaded_by']?.username,
            };

            // Push to 'all'
            categorizedFiles['all'].push(fileEntry);

            // Push to specific tab if valid
            const tabKey = typeToTabKey[doc.selection_type_id];
            if (tabKey) {
                categorizedFiles[tabKey].push(fileEntry);
            }
        });
    }
});

const isImage = (fileName: string): boolean => {
    const ext = fileName.split('.').pop()?.toLowerCase();
    return ['jpg', 'jpeg', 'png'].includes(ext ?? '');
};

function formatDate(dateString: string | null) {
    if (!dateString) return '-';
    return new Date(dateString).toISOString().slice(0, 10); // Formats as 'YYYY-MM-DD'
}

const statusLabel = (status: number) => {
    const labels = [
        'Pending Process', // 1
        'Pending Review', // 2
        'Pending Approval', // 3
        'Rejected', // 4
        'Approved', // 5
        'Customer Rejected', // 6
        'Customer Accepted', // 7
        'On-going', // 8
        'On-going (Overdue)', // 9
        'Completed', // 10
        'Cancelled', // 11
    ];
    return labels[status - 1] ?? 'Unknown';
};

const goToTransactionTab = () => {
    router.visit(`${route('loan.transaction', props.loan.id)}?from=show`);
};
</script>

<template>
    <AppLayout>
        <Head :title="`Loan: ${props.loan.code}`" />

        <div class="px-4 py-3">
            <Tabs default-value="loan-detail">
                <Heading title="Loans" pageNumber="P000013" description="View details of the loan record">
                    <template #status>
                        <Badge
                            :class="[
                                {
                                    'bg-ocean': props.loan.status === 1,
                                    'bg-canary': props.loan.status === 2,
                                    'bg-orange': props.loan.status === 3,
                                    'bg-chrome': props.loan.status === 4,
                                    'bg-castleton': props.loan.status === 5,
                                    'bg-pink': props.loan.status === 6,
                                    'bg-soften': props.loan.status === 7,
                                    'bg-cobalt': props.loan.status === 8,
                                    'bg-tomato': props.loan.status === 9,
                                    'bg-green': props.loan.status === 10,
                                    'bg-mist': props.loan.status === 11,
                                },
                                'text-md px-1 py-0',
                            ]"
                        >
                            {{ statusLabel(props.loan.status) }}
                        </Badge>
                    </template>
                    <template v-if="props.loan.status >= 7" #slot>
                        <div class="border-gainsboro border">
                            <Button type="button" @click="" class="bg-azure hover:bg-azure px-4 py-2 text-white hover:text-white"
                                ><FaIcon name="sack-dollar" class="pr-3" /> Loan Details</Button
                            >
                            <Button
                                type="button"
                                @click="goToTransactionTab"
                                class="bg-background hover:bg-background px-4 py-2 text-black hover:text-black"
                            >
                                <FaIcon name="right-left" class="pr-3" />Transaction</Button
                            >
                        </div>
                    </template>
                </Heading>

                <Card class="gap-0 py-0">
                    <CardHeader class="bg-azure gap-0 rounded-t-lg px-5.5 py-3 text-white">
                        <CardTitle>Loan No.: {{ props.loan.code }}</CardTitle>
                    </CardHeader>
                    <TabsContent value="loan-detail">
                        <TabsWrapper v-model="activeTab" :tabs="tabItems">
                            <template #personal>
                                <CardContent class="p-6">
                                    <Label class="text-[20px]" for="">Personal Details</Label>
                                    <div class="grid grid-cols-2 gap-3 lg:grid-cols-2">
                                        <div class="space-y-3">
                                            <div class="flex items-center justify-between px-1">
                                                <Label for="company-name" class="text-base">Company Name</Label>
                                                <p>{{ props.loan.company }}</p>
                                            </div>
                                            <div class="flex items-center justify-between px-1">
                                                <Label for="agent-name" class="text-base">Agent Name</Label>
                                                <p>{{ props.loan.agent }}</p>
                                            </div>
                                        </div>
                                        <div class="space-y-3">
                                            <div class="flex items-center justify-between px-1">
                                                <Label for="team-name" class="text-base">Team Name</Label>
                                                <p>{{ props.loan.team }}</p>
                                            </div>
                                            <div class="flex items-center justify-between px-1">
                                                <Label for="loan-type" class="text-base">Loan Type</Label>
                                                <p>{{ props.loan.type }}</p>
                                            </div>
                                        </div>
                                    </div>
                                    <FormShow :customer="props.loan.customer[0]" :ordinal="ordinal" />
                                    <Label class="text-[20px]" for="">Co-Borrower Info</Label>
                                    <FormShow
                                        v-for="(customer, index) in props.loan.customer.slice(1)"
                                        :label="`Co-Borrower ${index + 1}`"
                                        :customer="customer"
                                        :ordinal="ordinal"
                                    />
                                </CardContent>
                                <CardFooter class="bg-stone mb-0 flex items-center justify-end gap-2 rounded-b-xl px-4 py-3">
                                    <Button variant="outline" @click="goToNextTab" type="button" class="bg-cobalt flex items-center gap-2 text-white">
                                        Next
                                        <FaIcon name="chevron-right" />
                                    </Button>
                                </CardFooter>
                            </template>

                            <template #collateral>
                                <CardContent class="p-6">
                                    <Collateral
                                        :form="props.loan.loan_customer_collaterals"
                                        :collateralTypes="props.collateralTypes"
                                        :ordinal="ordinal"
                                        isAccordion
                                        isShow
                                    />
                                </CardContent>
                                <CardFooter class="bg-stone mb-0 flex items-center justify-end gap-2 rounded-b-xl px-4 py-3">
                                    <Button
                                        variant="outline"
                                        @click="goToPreviousTab"
                                        type="button"
                                        class="bg-card text-muted-foreground flex items-center gap-2"
                                    >
                                        <FaIcon name="chevron-left" /> Back
                                    </Button>
                                    <Button variant="outline" @click="goToNextTab" type="button" class="bg-cobalt flex items-center gap-2 text-white">
                                        Next
                                        <FaIcon name="chevron-right" />
                                    </Button>
                                </CardFooter>
                            </template>

                            <template #loan>
                                <CardContent class="p-6">
                                    <Label class="text-[20px]" for="">Loan Details</Label>
                                    <div class="grid grid-cols-2 gap-3 lg:grid-cols-2">
                                        <div class="space-y-3">
                                            <div class="flex items-center justify-between px-1">
                                                <Label for="loan-mode" class="text-base">Loan Mode</Label>
                                                <p>{{ props.loan.loanDetail.mode_type }}</p>
                                            </div>
                                            <div class="flex items-center justify-between px-1">
                                                <Label for="no-instalment" class="text-base">No of Instalment</Label>
                                                <p>{{ props.loan.loanDetail.no_of_instalment }}</p>
                                            </div>
                                            <div class="flex items-center justify-between px-1">
                                                <Label for="repayment-meyhod" class="text-base">Repayment Method</Label>
                                                <p>{{ props.loan.loanDetail.repayment_method }}</p>
                                            </div>
                                            <div v-if="props.loan.status >= 5" class="flex items-center justify-between px-1">
                                                <Label for="loan-commencement-date" class="text-base">Loan Commencement Date</Label>
                                                <p>{{ props.loan.loanDetail.commencement_date ?? '-' }}</p>
                                            </div>
                                            <div v-if="props.loan.status >= 5" class="flex items-center justify-between px-1">
                                                <Label for="next-due" class="text-base">Next Due Date</Label>
                                                <p>{{ props.loan.loanDetail.next_due_date ?? '-' }}</p>
                                            </div>
                                            <div v-if="props.loan.status >= 5" class="flex items-center justify-between px-1">
                                                <Label for="stamping-date" class="text-base">Stamping Date</Label>
                                                <p>{{ props.loan.loanDetail.stamping_date ?? '-' }}</p>
                                            </div>
                                        </div>
                                        <div class="space-y-3">
                                            <div class="flex items-center justify-between px-1">
                                                <Label for="loan-principle-amount" class="text-base">Loan Principle Amount (RM)</Label>
                                                <p>{{ props.loan.loanDetail.loan_principle_amount ?? '-' }}</p>
                                            </div>
                                            <div class="flex items-center justify-between px-1">
                                                <Label for="last-payment" class="text-base">Last Payment (RM)</Label>
                                                <p>{{ props.loan.loanDetail.last_payment ?? '-' }}</p>
                                            </div>
                                            <div v-if="props.loan.status >= 5" class="flex items-center justify-between px-1">
                                                <Label for="rebate" class="text-base">Rebate (RM)</Label>
                                                <p>{{ props.loan.loanDetail.rebate ?? '-' }}</p>
                                            </div>
                                            <div class="flex items-center justify-between px-1">
                                                <Label for="instalment-amount" class="text-base">Instalment Amount (RM)</Label>
                                                <p>{{ props.loan.loanDetail.instalment_amount }}</p>
                                            </div>
                                            <div class="flex items-center justify-between px-1">
                                                <Label for="interest" class="text-base">Interest (%)</Label>
                                                <p>{{ props.loan.loanDetail.interest }}</p>
                                            </div>
                                            <div v-if="props.loan.status >= 5" class="flex items-center justify-between px-1">
                                                <Label for="stamp-duty" class="text-base">Stamp Duty (RM)(-)</Label>
                                                <p>{{ props.loan.loanDetail.stamp_duty ?? '-' }}</p>
                                            </div>
                                            <div v-if="props.loan.status >= 5" class="flex items-center justify-between px-1">
                                                <Label for="attestation-fee" class="text-base">Attestation Fee (RM)(%)</Label>
                                                <p>{{ props.loan.loanDetail.attestation_fee ?? '-' }}</p>
                                            </div>
                                            <div v-if="props.loan.status >= 5" class="flex items-center justify-between px-1">
                                                <Label for="legal-fee" class="text-base">Legal Fee (RM)(-)</Label>
                                                <p>{{ props.loan.loanDetail.legal_fee ?? '-' }}</p>
                                            </div>
                                            <div v-if="props.loan.status >= 5" class="flex items-center justify-between px-1">
                                                <Label for="processing-fee" class="text-base">Processing Fee (RM)(-)</Label>
                                                <p>{{ props.loan.loanDetail.processing_fee ?? '-' }}</p>
                                            </div>
                                            <div v-if="props.loan.status >= 5" class="flex items-center justify-between px-1">
                                                <Label for="misc-charges" class="text-base">Misc Charges (%)</Label>
                                                <p>{{ props.loan.loanDetail.misc_charges ?? '-' }}</p>
                                            </div>
                                            <div class="flex items-center justify-between px-1">
                                                <Label for="late-payment-charges" class="text-base">Late Payment Charges (%)</Label>
                                                <p>{{ props.loan.loanDetail.late_payment_charges ?? '-' }}</p>
                                            </div>
                                            <div v-if="props.loan.status >= 5" class="flex items-center justify-between px-1">
                                                <Label for="loan-disbursement-amount" class="text-base">Loan Disbursement Amount (RM)</Label>
                                                <p>{{ props.loan.loanDetail.loan_disbursement_amount ?? '-' }}</p>
                                            </div>
                                        </div>
                                    </div>
                                </CardContent>
                                <CardFooter class="bg-stone mb-0 flex items-center justify-end gap-2 rounded-b-xl px-4 py-3">
                                    <Button
                                        variant="outline"
                                        @click="goToPreviousTab"
                                        type="button"
                                        class="bg-card text-muted-foreground flex items-center gap-2"
                                    >
                                        <FaIcon name="chevron-left" /> Back
                                    </Button>
                                    <Button variant="outline" @click="goToNextTab" type="button" class="bg-cobalt flex items-center gap-2 text-white">
                                        Next
                                        <FaIcon name="chevron-right" />
                                    </Button>
                                </CardFooter>
                            </template>

                            <template #emergency>
                                <CardContent class="p-6">
                                    <Label class="text-[20px]" for="">Emergency Contact</Label>
                                    <div class="grid grid-cols-2 gap-3 lg:grid-cols-2">
                                        <div class="space-y-3">
                                            <div class="flex items-center justify-between px-1">
                                                <Label for="name" class="text-base">Name</Label>
                                                <p>{{ props.loan.emergency.name ?? '-' }}</p>
                                            </div>
                                            <div class="flex items-center justify-between px-1">
                                                <Label for="identity-no" class="text-base">Identity No.</Label>
                                                <p>{{ props.loan.emergency.identity_no ?? '-' }}</p>
                                            </div>
                                            <div class="flex items-center justify-between px-1">
                                                <Label for="birth-date" class="text-base">Date of Birth</Label>
                                                <p>{{ formatDate(props.loan.emergency.birth_date) ?? '-' }}</p>
                                            </div>
                                            <div class="flex items-center justify-between px-1">
                                                <Label for="age" class="text-base">Age</Label>
                                                <p>{{ props.loan.emergency.age ?? '-' }}</p>
                                            </div>
                                            <div class="flex items-center justify-between px-1">
                                                <Label for="gender" class="text-base">Gender</Label>
                                                <p>{{ props.loan.emergency.gender_selection ?? '-' }}</p>
                                            </div>
                                            <div class="flex items-center justify-between px-1">
                                                <Label for="relationship" class="text-base">Relationship</Label>
                                                <p>{{ props.loan.emergency.relationship_selection ?? '-' }}</p>
                                            </div>
                                            <div class="flex items-center justify-between px-1">
                                                <Label for="nationality" class="text-base">Nationality</Label>
                                                <p>{{ props.loan.emergency.nationality_selection ?? '-' }}</p>
                                            </div>
                                        </div>
                                        <div class="space-y-3">
                                            <div class="flex items-start justify-between px-1">
                                                <Label for="contact" class="text-base">Contact</Label>
                                                <div v-if="props.loan.emergency.personal_contact">
                                                    <div v-if="props.loan.emergency.personal_contact.telephone">
                                                        ({{ props.loan.emergency.personal_contact.telephone_country_selection ?? '' }})
                                                        {{ props.loan.emergency.personal_contact.telephone ?? '-' }}
                                                    </div>
                                                    <div v-if="props.loan.emergency.personal_contact.mobile_phone">
                                                        ({{ props.loan.emergency.personal_contact.mobile_country_selection ?? '' }})
                                                        {{ props.loan.emergency.personal_contact.mobile_phone ?? '-' }}
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <Separator class="my-4" />
                                    <div class="space-y-3">
                                        <div class="px-1">
                                            <Label for="address" class="py-0 text-base">Address </Label>
                                            <p class="whitespace-pre-wrap">{{ formatAddress(props.loan.emergency.personal_address) }}</p>
                                        </div>
                                    </div>
                                    <Separator class="my-4" />
                                    <Label class="text-[20px]" for="">Emergency Contact's Employment Info</Label>
                                    <div class="grid grid-cols-2 gap-3 lg:grid-cols-2">
                                        <div class="space-y-3">
                                            <div class="flex items-center justify-between px-1">
                                                <Label for="employment-name" class="text-base">Name</Label>
                                                <p>{{ props.loan.emergency.employment_name ?? '-' }}</p>
                                            </div>
                                        </div>
                                        <div class="space-y-3">
                                            <div class="flex items-start justify-between px-1">
                                                <Label for="contact" class="text-base">Contact</Label>
                                                <div class="flex flex-col text-right">
                                                    <div v-if="props.loan.emergency.employment_contact">
                                                        <div v-if="props.loan.emergency.employment_contact.telephone">
                                                            ({{ props.loan.emergency.employment_contact.telephone_country_selection ?? '' }})
                                                            {{ props.loan.emergency.employment_contact.telephone ?? '-' }}
                                                        </div>
                                                        <div v-if="props.loan.emergency.employment_contact.mobile_phone">
                                                            ({{ props.loan.emergency.employment_contact.mobile_country_selection ?? '' }})
                                                            {{ props.loan.emergency.employment_contact.mobile_phone ?? '-' }}
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <Separator class="my-4" />
                                    <div class="space-y-3">
                                        <div class="px-1">
                                            <Label for="address" class="py-0 text-base">Address</Label>
                                            <p class="whitespace-pre-wrap">{{ formatAddress(props.loan.emergency.employment_address) }}</p>
                                        </div>
                                    </div>
                                    <Separator class="my-4" />
                                </CardContent>
                                <CardFooter class="bg-stone mb-0 flex items-center justify-end gap-2 rounded-b-xl px-4 py-3">
                                    <Button
                                        variant="outline"
                                        @click="goToPreviousTab"
                                        type="button"
                                        class="bg-card text-muted-foreground flex items-center gap-2"
                                    >
                                        <FaIcon name="chevron-left" /> Back
                                    </Button>
                                    <Button variant="outline" @click="goToNextTab" type="button" class="bg-cobalt flex items-center gap-2 text-white">
                                        Next
                                        <FaIcon name="chevron-right" />
                                    </Button>
                                </CardFooter>
                            </template>

                            <template #guarantor>
                                <CardContent class="p-6">
                                    <Label class="text-[20px]" for="">Guarantor Contact</Label>
                                    <Accordion type="single" class="w-full" collapsible>
                                        <AccordionItem
                                            v-for="(guarantor, index) in props.loan.guarantors"
                                            :key="index"
                                            :value="String(index)"
                                            class="mb-1"
                                        >
                                            <Card class="gap-0 rounded-xs py-0">
                                                <AccordionTrigger class="group flex w-full items-center justify-between gap-2 p-2 hover:no-underline">
                                                    <span class="bg-cloud p-1.5 group-data-[state=open]:hidden">
                                                        <FaIcon name="plus" />
                                                    </span>

                                                    <!-- Minus icon: visible when open -->
                                                    <span class="bg-cloud hidden p-1.5 group-data-[state=open]:block">
                                                        <FaIcon name="minus" />
                                                    </span>

                                                    <span class="flex-1 text-left font-medium"> {{ ordinal(index + 1) }} Guarantor </span>
                                                </AccordionTrigger>
                                                <Separator />
                                                <AccordionContent class="p-2">
                                                    <div class="grid grid-cols-2 gap-3 lg:grid-cols-2">
                                                        <div class="space-y-3">
                                                            <div class="flex items-center justify-between px-1">
                                                                <Label for="" class="text-base">Name</Label>
                                                                <p>{{ guarantor.name ?? '-' }}</p>
                                                            </div>
                                                            <div class="flex items-center justify-between px-1">
                                                                <Label for="" class="text-base">Identity No.</Label>
                                                                <p>{{ guarantor.identity_no ?? '-' }}</p>
                                                            </div>
                                                            <div class="flex items-center justify-between px-1">
                                                                <Label for="" class="text-base">Date of Birth</Label>
                                                                <p>{{ formatDate(guarantor.birth_date) ?? '-' }}</p>
                                                            </div>
                                                            <div class="flex items-center justify-between px-1">
                                                                <Label for="" class="text-base">Age</Label>
                                                                <p>{{ guarantor.age ?? '-' }}</p>
                                                            </div>
                                                            <div class="flex items-center justify-between px-1">
                                                                <Label for="" class="text-base">Gender</Label>
                                                                <p>{{ guarantor.gender ?? '-' }}</p>
                                                            </div>
                                                            <div class="flex items-center justify-between px-1">
                                                                <Label for="" class="text-base">Relationship</Label>
                                                                <p>{{ guarantor.relationship ?? '-' }}</p>
                                                            </div>
                                                            <div class="flex items-center justify-between px-1">
                                                                <Label for="" class="text-base">Nationality</Label>
                                                                <p>{{ guarantor.nationality ?? '-' }}</p>
                                                            </div>
                                                        </div>
                                                        <div class="space-y-3">
                                                            <div class="flex items-start justify-between px-1">
                                                                <Label for="contact" class="text-base">Contact</Label>
                                                                <div class="flex flex-col text-right">
                                                                    <div v-if="guarantor.personal_contact">
                                                                        <div v-if="guarantor.personal_contact.telephone">
                                                                            ({{ guarantor.personal_contact.telephone_country_selection ?? '' }})
                                                                            {{ guarantor.personal_contact.telephone ?? '-' }}
                                                                        </div>
                                                                        <div v-if="guarantor.personal_contact.mobile_phone">
                                                                            ({{ guarantor.personal_contact.mobile_country_selection ?? '' }})
                                                                            {{ guarantor.personal_contact.mobile_phone ?? '-' }}
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <Separator class="my-4" />
                                                    <div class="space-y-3">
                                                        <div class="px-1">
                                                            <Label for="address" class="py-0 text-base">Address</Label>
                                                            <p class="whitespace-pre-wrap">{{ formatAddress(guarantor.personal_address) }}</p>
                                                        </div>
                                                    </div>
                                                    <Separator class="my-4" />
                                                    <Label class="text-[20px]" for="">Guarantor Contact's Employment Info</Label>
                                                    <div class="grid grid-cols-2 gap-3 lg:grid-cols-2">
                                                        <div class="space-y-3">
                                                            <div class="flex items-center justify-between px-1">
                                                                <Label for="" class="text-base">Terms of Employment</Label>
                                                                <p>{{ guarantor.terms_of_employment ?? '-' }}</p>
                                                            </div>
                                                            <div class="flex items-center justify-between px-1">
                                                                <Label for="" class="text-base">Employment Name</Label>
                                                                <p>{{ guarantor.employment_name ?? '-' }}</p>
                                                            </div>
                                                            <div class="flex items-center justify-between px-1">
                                                                <Label for="" class="text-base">Job Position</Label>
                                                                <p>{{ guarantor.job_position ?? '-' }}</p>
                                                            </div>
                                                            <div class="flex items-center justify-between px-1">
                                                                <Label for="" class="text-base">Length Service Year</Label>
                                                                <p>{{ guarantor.length_service_year ?? '-' }}</p>
                                                            </div>
                                                            <div class="flex items-center justify-between px-1">
                                                                <Label for="" class="text-base">Length Service Month</Label>
                                                                <p>{{ guarantor.length_service_month ?? '-' }}</p>
                                                            </div>
                                                            <div class="flex items-center justify-between px-1">
                                                                <Label for="" class="text-base">Business Classification</Label>
                                                                <p>{{ guarantor.business_classification ?? '-' }}</p>
                                                            </div>
                                                            <div class="flex items-center justify-between px-1">
                                                                <Label for="" class="text-base">Occupation</Label>
                                                                <p>{{ guarantor.occupation ?? '-' }}</p>
                                                            </div>
                                                        </div>
                                                        <div class="space-y-3">
                                                            <div class="flex items-start justify-between px-1">
                                                                <Label for="contact" class="text-base">Contact</Label>
                                                                <div class="flex flex-col text-right">
                                                                    <div v-if="guarantor.employment_contact">
                                                                        <div v-if="guarantor.employment_contact.telephone">
                                                                            ({{ guarantor.employment_contact.telephone_country_selection ?? '' }})
                                                                            {{ guarantor.employment_contact.telephone ?? '-' }}
                                                                        </div>
                                                                        <div v-if="guarantor.employment_contact.mobile_phone">
                                                                            ({{ guarantor.employment_contact.mobile_country_selection ?? '' }})
                                                                            {{ guarantor.employment_contact.mobile_phone ?? '-' }}
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <Separator class="my-4" />
                                                    <div class="space-y-3">
                                                        <div class="px-1">
                                                            <Label for="address" class="py-0 text-base">Address</Label>
                                                            <p class="whitespace-pre-wrap">{{ formatAddress(guarantor.employment_address) }}</p>
                                                        </div>
                                                    </div>
                                                </AccordionContent>
                                            </Card>
                                        </AccordionItem>
                                    </Accordion>
                                </CardContent>
                                <CardFooter class="bg-stone mb-0 flex items-center justify-end gap-2 rounded-b-xl px-4 py-3">
                                    <Button
                                        variant="outline"
                                        @click="goToPreviousTab"
                                        type="button"
                                        class="bg-card text-muted-foreground flex items-center gap-2"
                                    >
                                        <FaIcon name="chevron-left" /> Back
                                    </Button>
                                    <Button variant="outline" @click="goToNextTab" type="button" class="bg-cobalt flex items-center gap-2 text-white">
                                        Next
                                        <FaIcon name="chevron-right" />
                                    </Button>
                                </CardFooter>
                            </template>

                            <template #bank>
                                <CardContent class="p-6">
                                    <Label class="text-[20px]" for="">Disbursement Account</Label>
                                    <Table>
                                        <TableHeader>
                                            <TableRow class="bg-white">
                                                <TableHead class="border-sort border px-3 py-0 text-center font-semibold text-black">
                                                    Account Type
                                                </TableHead>
                                                <TableHead class="border-sort border px-3 py-0 text-center font-semibold text-black">
                                                    Bank Name
                                                </TableHead>
                                                <TableHead class="border-sort border px-3 py-0 text-center font-semibold text-black">
                                                    Bank Holder Name
                                                </TableHead>
                                                <TableHead class="border-sort border px-3 py-0 text-center font-semibold text-black">
                                                    Bank Account Number
                                                </TableHead>
                                            </TableRow>
                                        </TableHeader>
                                        <TableBody>
                                            <TableRow
                                                v-for="bank in props.loan.bankDetails"
                                                class="group even:bg-cement last:!border-sort transition-colors last:!border-b-1 odd:bg-white"
                                            >
                                                <TableCell class="border-sort border-x px-3 py-1.5">
                                                    {{ bank.type ?? bank.type_selection }}
                                                </TableCell>
                                                <TableCell class="border-sort border-x px-3 py-1.5">
                                                    {{ bank.bank ?? bank.bank_selection }}
                                                </TableCell>
                                                <TableCell class="border-sort border-x px-3 py-1.5">
                                                    {{ bank.account_name }}
                                                </TableCell>
                                                <TableCell class="border-sort border-x px-3 py-1.5">
                                                    {{ bank.account_no }}
                                                </TableCell>
                                            </TableRow>
                                        </TableBody>
                                    </Table>
                                </CardContent>
                                <CardFooter class="bg-stone mb-0 flex items-center justify-end gap-2 rounded-b-xl px-4 py-3">
                                    <Button
                                        variant="outline"
                                        @click="goToPreviousTab"
                                        type="button"
                                        class="bg-card text-muted-foreground flex items-center gap-2"
                                    >
                                        <FaIcon name="chevron-left" /> Back
                                    </Button>
                                    <Button variant="outline" @click="goToNextTab" type="button" class="bg-cobalt flex items-center gap-2 text-white">
                                        Next
                                        <FaIcon name="chevron-right" />
                                    </Button>
                                </CardFooter>
                            </template>

                            <template #document>
                                <CardContent class="py-4">
                                    <div class="flex gap-4">
                                        <div class="flex w-[75%] flex-col gap-2">
                                            <Card class="h-[420px] w-full rounded-lg py-2">
                                                <CardContent class="px-2">
                                                    <TabsWrapper v-model="activeDocTab" :tabs="docItems">
                                                        <template #all>
                                                            <CardContent class="px-0">
                                                                <div
                                                                    v-if="categorizedFiles['all'].length == 0"
                                                                    class="flex h-[380px] items-center justify-center"
                                                                >
                                                                    <p>No Documents</p>
                                                                </div>
                                                                <div
                                                                    v-else
                                                                    class="grid grid-cols-2 gap-2 py-2 text-center sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5"
                                                                >
                                                                    <div
                                                                        v-for="(doc, index) in categorizedFiles['all']"
                                                                        :key="doc.id"
                                                                        @click="toggleSelect(doc.id)"
                                                                        :class="[
                                                                            'relative flex cursor-pointer flex-col items-center justify-center rounded border p-2 transition',
                                                                            selectedDocIds.includes(doc.id)
                                                                                ? 'border-blue-500 bg-blue-50'
                                                                                : 'border-transparent hover:border-gray-300',
                                                                        ]"
                                                                    >
                                                                        <div
                                                                            v-if="selectedDocIds.includes(doc.id)"
                                                                            class="bg-azure absolute top-1 right-1 flex h-5 w-5 items-center justify-center rounded-none text-white"
                                                                        >
                                                                            <span class="text-xs">
                                                                                <FaIcon name="check" class="text-white" />
                                                                            </span>
                                                                        </div>

                                                                        <!-- Thumbnail -->
                                                                        <template v-if="isImage(doc.name)">
                                                                            <img
                                                                                :src="doc.url"
                                                                                alt="preview"
                                                                                class="h-[85px] w-[150px] object-contain"
                                                                            />
                                                                        </template>
                                                                        <template v-else>
                                                                            <span class="text-[35px]">
                                                                                <FaIcon
                                                                                    name="file-pdf"
                                                                                    class="h-[85px] w-[150px] object-contain text-blue-500"
                                                                                />
                                                                            </span>
                                                                        </template>

                                                                        <!-- File name -->
                                                                        <a
                                                                            :href="doc.url"
                                                                            target="_blank"
                                                                            class="max-w-[100px] truncate text-sm text-blue-700 hover:underline"
                                                                        >
                                                                            {{ doc.name }}
                                                                        </a>
                                                                    </div>
                                                                </div>
                                                            </CardContent>
                                                        </template>

                                                        <template #customer-doc>
                                                            <CardContent class="px-0">
                                                                <div
                                                                    v-if="categorizedFiles['customer-doc'].length == 0"
                                                                    class="flex h-[380px] items-center justify-center"
                                                                >
                                                                    <p>No Documents</p>
                                                                </div>
                                                                <div
                                                                    v-else
                                                                    class="grid grid-cols-2 gap-2 py-2 text-center sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5"
                                                                >
                                                                    <div
                                                                        v-for="(doc, index) in categorizedFiles['customer-doc']"
                                                                        :key="doc.id"
                                                                        @click="toggleSelect(doc.id)"
                                                                        :class="[
                                                                            'relative flex cursor-pointer flex-col items-center justify-center rounded border p-2 transition',
                                                                            selectedDocIds.includes(doc.id)
                                                                                ? 'border-blue-500 bg-blue-50'
                                                                                : 'border-transparent hover:border-gray-300',
                                                                        ]"
                                                                    >
                                                                        <div
                                                                            v-if="selectedDocIds.includes(doc.id)"
                                                                            class="bg-azure absolute top-1 right-1 flex h-5 w-5 items-center justify-center rounded-none text-white"
                                                                        >
                                                                            <span class="text-xs">
                                                                                <FaIcon name="check" class="text-white" />
                                                                            </span>
                                                                        </div>

                                                                        <!-- Thumbnail -->
                                                                        <template v-if="isImage(doc.name)">
                                                                            <img
                                                                                :src="doc.url"
                                                                                alt="preview"
                                                                                class="h-[85px] w-[150px] object-contain"
                                                                            />
                                                                        </template>
                                                                        <template v-else>
                                                                            <span class="text-[35px]">
                                                                                <FaIcon
                                                                                    name="file-pdf"
                                                                                    class="h-[85px] w-[150px] object-contain text-blue-500"
                                                                                />
                                                                            </span>
                                                                        </template>

                                                                        <!-- File name -->
                                                                        <a
                                                                            :href="doc.url"
                                                                            target="_blank"
                                                                            class="max-w-[100px] truncate text-sm text-blue-700 hover:underline"
                                                                        >
                                                                            {{ doc.name }}
                                                                        </a>
                                                                    </div>
                                                                </div>
                                                            </CardContent>
                                                        </template>

                                                        <template #collateral-doc>
                                                            <CardContent class="px-0">
                                                                <div
                                                                    v-if="categorizedFiles['collateral-doc'].length == 0"
                                                                    class="flex h-[380px] items-center justify-center"
                                                                >
                                                                    <p>No Documents</p>
                                                                </div>
                                                                <div
                                                                    v-else
                                                                    class="grid grid-cols-2 gap-2 py-2 text-center sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5"
                                                                >
                                                                    <div
                                                                        v-for="(doc, index) in categorizedFiles['collateral-doc']"
                                                                        :key="doc.id"
                                                                        @click="toggleSelect(doc.id)"
                                                                        :class="[
                                                                            'relative flex cursor-pointer flex-col items-center justify-center rounded border p-2 transition',
                                                                            selectedDocIds.includes(doc.id)
                                                                                ? 'border-blue-500 bg-blue-50'
                                                                                : 'border-transparent hover:border-gray-300',
                                                                        ]"
                                                                    >
                                                                        <div
                                                                            v-if="selectedDocIds.includes(doc.id)"
                                                                            class="bg-azure absolute top-1 right-1 flex h-5 w-5 items-center justify-center rounded-none text-white"
                                                                        >
                                                                            <span class="text-xs">
                                                                                <FaIcon name="check" class="text-white" />
                                                                            </span>
                                                                        </div>

                                                                        <!-- Thumbnail -->
                                                                        <template v-if="isImage(doc.name)">
                                                                            <img
                                                                                :src="doc.url"
                                                                                alt="preview"
                                                                                class="h-[85px] w-[150px] object-contain"
                                                                            />
                                                                        </template>
                                                                        <template v-else>
                                                                            <span class="text-[35px]">
                                                                                <FaIcon
                                                                                    name="file-pdf"
                                                                                    class="h-[85px] w-[150px] object-contain text-blue-500"
                                                                                />
                                                                            </span>
                                                                        </template>

                                                                        <!-- File name -->
                                                                        <a
                                                                            :href="doc.url"
                                                                            target="_blank"
                                                                            class="max-w-[100px] truncate text-sm text-blue-700 hover:underline"
                                                                        >
                                                                            {{ doc.name }}
                                                                        </a>
                                                                    </div>
                                                                </div>
                                                            </CardContent>
                                                        </template>

                                                        <template #security-doc>
                                                            <CardContent class="px-0">
                                                                <div
                                                                    v-if="categorizedFiles['security-doc'].length == 0"
                                                                    class="flex h-[380px] items-center justify-center"
                                                                >
                                                                    <p>No Documents</p>
                                                                </div>
                                                                <div
                                                                    v-else
                                                                    class="grid grid-cols-2 gap-2 py-2 text-center sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5"
                                                                >
                                                                    <div
                                                                        v-for="(doc, index) in categorizedFiles['security-doc']"
                                                                        :key="doc.id"
                                                                        @click="toggleSelect(doc.id)"
                                                                        :class="[
                                                                            'relative flex cursor-pointer flex-col items-center justify-center rounded border p-2 transition',
                                                                            selectedDocIds.includes(doc.id)
                                                                                ? 'border-blue-500 bg-blue-50'
                                                                                : 'border-transparent hover:border-gray-300',
                                                                        ]"
                                                                    >
                                                                        <div
                                                                            v-if="selectedDocIds.includes(doc.id)"
                                                                            class="bg-azure absolute top-1 right-1 flex h-5 w-5 items-center justify-center rounded-none text-white"
                                                                        >
                                                                            <span class="text-xs">
                                                                                <FaIcon name="check" class="text-white" />
                                                                            </span>
                                                                        </div>

                                                                        <!-- Thumbnail -->
                                                                        <template v-if="isImage(doc.name)">
                                                                            <img
                                                                                :src="doc.url"
                                                                                alt="preview"
                                                                                class="h-[85px] w-[150px] object-contain"
                                                                            />
                                                                        </template>
                                                                        <template v-else>
                                                                            <span class="text-[35px]">
                                                                                <FaIcon
                                                                                    name="file-pdf"
                                                                                    class="h-[85px] w-[150px] object-contain text-blue-500"
                                                                                />
                                                                            </span>
                                                                        </template>

                                                                        <!-- File name -->
                                                                        <a
                                                                            :href="doc.url"
                                                                            target="_blank"
                                                                            class="max-w-[100px] truncate text-sm text-blue-700 hover:underline"
                                                                        >
                                                                            {{ doc.name }}
                                                                        </a>
                                                                    </div>
                                                                </div>
                                                            </CardContent>
                                                        </template>
                                                    </TabsWrapper>
                                                </CardContent>
                                            </Card>
                                        </div>
                                        <div class="w-[25%]">
                                            <Card class="flex h-[500px]" v-if="selectedDocIds.length > 0 && selectedFile">
                                                <CardContent>
                                                    <div class="mt-4 w-full max-w-md">
                                                        <a
                                                            :href="selectedFile.url"
                                                            target="_blank"
                                                            class="mb-3 block truncate text-sm text-blue-700 hover:underline"
                                                        >
                                                            {{ selectedFile.name }}
                                                        </a>

                                                        <!-- Icon -->
                                                        <div v-if="isImage(selectedFile.name)" class="mb-6 text-center">
                                                            <img
                                                                :src="selectedFile.url"
                                                                alt="preview"
                                                                class="h-auto max-h-[150px] w-full object-contain"
                                                            />
                                                        </div>
                                                        <div v-else class="mb-6 text-center">
                                                            <span class="text-lavender text-[50px]">
                                                                <FaIcon name="file" />
                                                            </span>
                                                        </div>

                                                        <!-- Details -->
                                                        <div class="space-y-4 text-sm">
                                                            <div>
                                                                <p class="font-semibold">File Type:</p>
                                                                <p>{{ selectedFile.type }}</p>
                                                            </div>
                                                            <div>
                                                                <p class="font-semibold">File Size:</p>
                                                                <p>{{ (selectedFile.size / 1024).toFixed(2) }} KB</p>
                                                            </div>
                                                            <div>
                                                                <p class="font-semibold">Created Date:</p>
                                                                <p>{{ selectedFile.createdAt }}</p>
                                                            </div>
                                                            <div>
                                                                <p class="font-semibold">Uploaded By:</p>
                                                                <p>{{ selectedFile.uploadedBy }}</p>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </CardContent>
                                            </Card>
                                            <Card v-else class="flex h-[500px] items-center justify-center">
                                                <CardContent>
                                                    <div class="text-center">
                                                        <span class="text-lavender text-[50px]">
                                                            <FaIcon name="file" />
                                                        </span>
                                                        <p>0 Item</p>
                                                        <p>Select a single file to get more information</p>
                                                    </div>
                                                </CardContent>
                                            </Card>
                                        </div>
                                    </div>
                                </CardContent>
                                <CardFooter class="bg-stone mb-0 flex items-center justify-end gap-2 rounded-b-xl px-4 py-3">
                                    <Button
                                        variant="outline"
                                        @click="goToPreviousTab"
                                        type="button"
                                        class="bg-card text-muted-foreground flex items-center gap-2"
                                    >
                                        <FaIcon name="chevron-left" /> Back
                                    </Button>
                                </CardFooter>
                            </template>
                        </TabsWrapper>
                    </TabsContent>
                    <TabsContent value="transaction">
                        <TabsWrapper v-model="activeTransactionTab" :tabs="tabTransactionItems">
                            <template #general>
                                <CardContent class="px-6 py-2">
                                    <Label class="text-[20px]" for="">General</Label>
                                    <Label class="py-4 text-[20px]" for="">Loan Details</Label>
                                    <div class="grid grid-cols-1 gap-3 lg:grid-cols-1">
                                        <div class="space-y-3">
                                            <div class="flex items-center justify-between">
                                                <Label for="company-name" class="text-base font-normal">Loan Release Date</Label>
                                                <p>123</p>
                                            </div>
                                            <div class="flex items-center justify-between">
                                                <Label for="company-name" class="text-base font-normal">Loan Type</Label>
                                                <p>{{ props.loan.type }}</p>
                                            </div>
                                            <div class="flex items-center justify-between">
                                                <Label for="company-name" class="text-base font-normal">Loan Mode</Label>
                                                <p>{{ props.loan.loanDetail.mode_type }}</p>
                                            </div>
                                            <div class="flex items-center justify-between">
                                                <Label for="company-name" class="text-base font-normal">Instalment Amount (RM)</Label>
                                                <p>{{ props.loan.loanDetail.instalment_amount }}</p>
                                            </div>
                                            <div class="flex items-center justify-between">
                                                <Label for="company-name" class="text-base font-normal">Last Payment Amount (RM)</Label>
                                                <p>{{ props.loan.loanDetail.last_payment }}</p>
                                            </div>
                                            <div class="flex items-center justify-between">
                                                <Label for="company-name" class="text-base font-normal">No of Instalment (Tenure)</Label>
                                                <p>{{ props.loan.loanDetail.no_of_instalment }}</p>
                                            </div>
                                            <div class="flex items-center justify-between">
                                                <Label for="company-name" class="text-base font-normal">Instalment Arrears (RM)</Label>
                                                <p>123</p>
                                            </div>
                                            <div class="flex items-center justify-between">
                                                <Label for="company-name" class="text-base font-normal">Current Due Date</Label>
                                                <p>123</p>
                                            </div>
                                        </div>
                                    </div>
                                    <Label class="py-4 text-[20px]" for="">Loan Balance Status</Label>
                                    <div class="grid grid-cols-1 gap-3 lg:grid-cols-1">
                                        <div class="space-y-3">
                                            <div class="flex items-center justify-between">
                                                <Label for="company-name" class="text-base font-normal">Loan Principle Amount</Label>
                                                <p>{{ props.loan.loanDetail.loan_principle_amount }}</p>
                                            </div>
                                            <div class="flex items-center justify-between">
                                                <Label for="company-name" class="text-base font-normal">Interest Charges (+)</Label>
                                                <p>{{ props.loan.loanDetail.interest }}</p>
                                            </div>
                                            <Separator class="my-4" />
                                            <div class="flex items-center justify-between">
                                                <Label for="company-name" class="text-base">Balance Payable (RM)</Label>
                                                <p>123</p>
                                            </div>
                                            <div class="flex items-center justify-between">
                                                <Label for="company-name" class="text-base font-normal">Total Instalment Paid (RM)(-)</Label>
                                                <p>123</p>
                                            </div>
                                            <Separator class="my-4" />
                                            <div class="flex items-center justify-between">
                                                <Label for="company-name" class="text-base">Instalment Balance (RM)</Label>
                                                <p>123</p>
                                            </div>
                                            <div class="flex items-center justify-between">
                                                <Label for="company-name" class="text-base font-normal">Late Interest Charges (RM)(+)</Label>
                                                <p>123</p>
                                            </div>
                                            <div class="flex items-center justify-between">
                                                <Label for="company-name" class="text-base font-normal">Postage Charges (RM)(+)</Label>
                                                <p>123</p>
                                            </div>
                                            <div class="flex items-center justify-between">
                                                <Label for="company-name" class="text-base font-normal">Legal Fee (RM)(+)</Label>
                                                <p>123</p>
                                            </div>
                                            <div class="flex items-center justify-between">
                                                <Label for="company-name" class="text-base font-normal">Misc Charges (RM)(+)</Label>
                                                <p>123</p>
                                            </div>
                                            <div class="flex items-center justify-between">
                                                <Label for="company-name" class="text-base font-normal">Rebate (RM)(-)</Label>
                                                <p>123</p>
                                            </div>
                                            <Separator class="my-4" />
                                            <div class="flex items-center justify-between">
                                                <Label for="company-name" class="text-base">Current Balance (RM)</Label>
                                                <p>123</p>
                                            </div>
                                        </div>
                                    </div>
                                </CardContent>
                                <!-- TODO: dataTable for loanDetail -->
                            </template>
                            <template #transaction>
                                <CardContent class="">
                                    <div class="grid grid-cols-1 gap-3 lg:grid-cols-1">
                                        <div class="space-y-3">
                                            <Label class="pt-2 text-[20px]" for="">Current Instalment Info</Label>
                                            <div class="flex items-center justify-between">
                                                <Label for="" class="text-base font-normal">Current Instalment No</Label>
                                                <p>123</p>
                                            </div>
                                            <div class="flex items-center justify-between">
                                                <Label for="" class="text-base font-normal">Instalment Owe (RM)</Label>
                                                <p>123</p>
                                            </div>
                                            <div class="flex items-center justify-between">
                                                <Label for="" class="text-base font-normal">Late Charges (RM)</Label>
                                                <p>123</p>
                                            </div>
                                            <div class="flex items-center justify-between">
                                                <Label for="" class="text-base font-normal">Postage Charges (RM)</Label>
                                                <p>123</p>
                                            </div>
                                            <div class="flex items-center justify-between">
                                                <Label for="" class="text-base font-normal">Legal Fee (RM)</Label>
                                                <p>123</p>
                                            </div>
                                            <div class="flex items-center justify-between">
                                                <Label for="" class="text-base font-normal">Misc Charges (RM)</Label>
                                                <p>123</p>
                                            </div>
                                            <Separator class="my-4" />
                                            <div class="flex items-center justify-between">
                                                <Label for="" class="text-base">Total (RM)</Label>
                                                <p>123</p>
                                            </div>
                                        </div>
                                    </div>
                                    <Label class="pt-2 text-[20px]" for="">Unpaid Payment</Label>
                                    <!-- ToDo: dataTable for unpaid loanDetail payment -->
                                </CardContent>
                            </template>
                            <template #transaction-record>
                                <CardContent class="px-6 py-2">
                                    <Label class="text-[20px]" for="">Transaction Records</Label>
                                    <!-- ToDo: dataTAble for all paid loanDetail and able to do print -->
                                </CardContent>
                            </template>
                        </TabsWrapper>
                    </TabsContent>
                </Card>
            </Tabs>
        </div>
    </AppLayout>
</template>
