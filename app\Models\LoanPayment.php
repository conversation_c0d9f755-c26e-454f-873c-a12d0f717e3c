<?php

namespace App\Models;

use App\Traits\DateTimeConversion;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class LoanPayment extends BaseModel
{
    use DateTimeConversion, HasFactory;

    protected $fillable = [
        'uuid',
        'txn_date',
        'txn_type',
        'payment_ref_code',
        'payment_method',
        'payment_date',
        'amount',
        'rebate_amount',
        'remark',
        'status',
        'created_by',
        'updated_by',
        'deleted_by',
    ];

    protected $casts = [
        'txn_date' => 'datetime',
        'payment_date' => 'datetime',
        'amount' => 'decimal:2',
        'rebate_amount' => 'decimal:2',
    ];
}
