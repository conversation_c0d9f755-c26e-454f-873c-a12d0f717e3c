<?php

namespace App\Http\Controllers\Loans;

use App\Enums\Loan\LoanStatus;
use App\Enums\Loan\LoanTxnStatus;
use App\Http\Controllers\Controller;
use App\Http\Requests\Loans\StoreLoanRequest;
use App\Http\Requests\Loans\UpdateLoanRequest;
use App\Http\Resources\Collaterals\CollateralDetailResource;
use App\Http\Resources\Loans\LoanDetailResource;
use App\Http\Resources\Loans\LoanInstallmentResource;
use App\Http\Resources\Loans\LoanPaymentResource;
use App\Http\Resources\Loans\LoanResource;
use App\Http\Resources\Loans\LoanTransactionResource;
use App\Models\AgentProfile;
use App\Models\Collateral;
use App\Models\CollateralProperty;
use App\Models\CollateralPropertyOwner;
use App\Models\Company;
use App\Models\Contact;
use App\Models\CustomerCollateral;
use App\Models\CustomerDocument;
use App\Models\CustomerProfile;
use App\Models\Headquarter;
use App\Models\Loan;
use App\Models\LoanBankAccount;
use App\Models\LoanCustomerCollateral;
use App\Models\LoanDocument;
use App\Models\LoanEmergency;
use App\Models\LoanGuarantor;
use App\Models\LoanInstallment;
use App\Models\LoanTxn;
use App\Models\Selection;
use App\Models\Team;
use App\Traits\HandlesFileStorage;
use App\Traits\QueryFilterableTrait;
use App\Traits\SelectionTrait;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Redirect;
use Inertia\Inertia;
use Inertia\Response;

class LoanController extends Controller
{
    use HandlesFileStorage, QueryFilterableTrait, SelectionTrait;

    /**
     * Display a listing of the loans.
     */
    public function index(Request $request): Response
    {
        $this->authorize('viewAny', Loan::class);

        $query = Loan::query()
            ->withAuditUsers()
            ->with([
                'loanCustomerProfiles',
                'loanCustomerProfiles.loanCustomerContacts.contacts',
                'company:id,code,display_name',
                'team:id,name',
                'selectionType:id,value',
            ])
            ->when($request->filled('name'), function ($query) use ($request) {
                return $query->whereHas('loanCustomerProfiles', function ($q) use ($request) {
                    $q->where('name', 'like', "%{$request->name}%");
                });
            })
            ->when($request->filled('identity_no'), function ($query) use ($request) {
                return $query->whereHas('loanCustomerProfiles', function ($q) use ($request) {
                    $q->where('identity_no', 'like', "%{$request->identity_no}%");
                });
            });

        $this->applySearchFilter($query, $request, 'code');
        $this->applyRelationFilter($query, $request, 'team_name', 'team', 'name');
        $this->applyStatusFilter($query, $request);
        $this->applySorting($query, $request);

        $loans = $this->applyPagination($query, $request, 10,
            fn ($loan) => (new LoanResource($loan))->toArray($request));

        return Inertia::render('loans/Index', [
            'loans' => $loans,
            'filters' => $request->only(['code', 'name', 'company_name', 'team_name', 'status', 'per_page', 'sort_field', 'sort_direction']),
            'statuses' => LoanStatus::options(),
        ]);
    }

    /**
     * Show the form for creating a new loan.
     */
    public function create(): Response
    {
        $this->authorize('create', Loan::class);

        return Inertia::render('loans/Create', [
            'headquarters' => Headquarter::getForDropdown(),
            'companies' => Company::getForDropdown(),
            'teams' => Team::getForDropdown(),
            'agents' => AgentProfile::getForDropdown(),
            'loanTypes' => $this->getSelectionsOptionsForCategory('loan_type'),
            'loanModes' => $this->getSelectionsOptionsForCategory('loan_mode'),
            'repaymentMethods' => $this->getSelectionsOptionsForCategory('repayment_method'),
            'relationships' => $this->getSelectionsOptionsForCategory('relationship'),
            'telephoneCountries' => $this->getSelectionsOptionsForCategory('telephone_country'),
            'mobileCountries' => $this->getSelectionsOptionsForCategory('mobile_country'),
            'genderTypes' => $this->getSelectionsOptionsForCategory('gender'),
            'contactTypes' => $this->getSelectionsOptionsForCategory('contact_type'),
            'states' => $this->getSelectionsOptionsForCategory('state'),
            'countries' => $this->getSelectionsOptionsForCategory('country'),
            'nationalities' => $this->getSelectionsOptionsForCategory('nationality'),
            'employmentTerms' => $this->getSelectionsOptionsForCategory('terms_of_employment'),
            'businessClassifications' => $this->getSelectionsOptionsForCategory('business_classification'),
            'occupations' => $this->getSelectionsOptionsForCategory('occupation'),
            'documentTypes' => $this->getSelectionsOptionsForCategory('document_type'),
            'collateralTypes' => $this->getSelectionsOptionsForCategory('collateral_type'),
        ]);
    }

    /**
     * Store a newly created loan in storage.
     */
    public function store(StoreLoanRequest $request): RedirectResponse
    {
        $this->authorize('create', Loan::class);

        try {
            DB::beginTransaction();

            $company = Company::find($request->company_id);
            $team = Team::find($request->team_id);
            $agent = AgentProfile::find($request->agent_id);
            $selectionLoanType = Selection::find($request->selection_type_id);

            // Create loan
            $loan = Loan::create([
                'company_id' => $company?->id,
                'company' => $company?->display_name,
                'team_id' => $team?->id,
                'team' => $team?->name,
                'agent_id' => $agent?->id,
                'agent' => $agent?->display_name,
                'selection_type_id' => $selectionLoanType?->id,
                'type' => $selectionLoanType?->value,
                'status' => $request->boolean('_saveAsDraft') ? LoanStatus::PENDING_PROCESS : LoanStatus::PENDING_REVIEW,
            ]);

            $selectionModeType = Selection::find($request->input('loan.selection_mode_type_id'));
            $selectionRepaymentMethod = Selection::find($request->input('loan.selection_repayment_method_id'));

            // Create loan detail
            $loan->loanDetail()->create([
                'selection_mode_type_id' => $selectionModeType?->id,
                'mode_type' => $selectionModeType?->value,
                'loan_principle_amount' => $request->input('loan.loan_principle_amount'),
                'no_of_instalment' => $request->input('loan.no_of_instalment'),
                'last_payment' => $request->input('loan.last_payment'),
                'selection_repayment_method_id' => $selectionRepaymentMethod?->id,
                'repayment_method' => $selectionRepaymentMethod?->value,
                'instalment_amount' => $request->input('loan.instalment_amount'),
                'interest' => $request->input('loan.interest'),
                'late_payment_charges' => $request->input('loan.late_payment_charges'),
            ]);

            $borrower = CustomerProfile::with([
                'employment.address', 'employment.contacts',
                'company.owners',
                'customerAddresses.addresses', 'customerContacts.contacts',
            ])->find($request->borrower_id);

            $selectionBorrowerType = Selection::find($borrower->selection_type_id);
            $selectionBorrowerGender = Selection::find($borrower->selection_gender_id);
            $selectionBorrowerRace = Selection::find($borrower->selection_race_id);
            $selectionBorrowerNationality = Selection::find($borrower->selection_nationality_id);
            $selectionBorrowerEducationLevel = Selection::find($borrower->selection_education_level_id);
            $selectionBorrowerMarriageStatus = Selection::find($borrower->selection_marriage_status_id);

            // Create main borrower profile
            $loanBorrowerProfile = $loan->loanCustomerProfiles()->create([
                'customer_id' => $borrower->id,
                'code' => $borrower->code,
                'name' => $borrower->name,
                'email' => $borrower->email,
                'identity_no' => $borrower->identity_no,
                'old_identity_no' => $borrower->old_identity_no,
                'registration_date' => $borrower->registration_date,
                'years_of_incorporation' => $borrower->years_of_incorporation,
                'age' => $borrower->age,
                'birth_date' => $borrower->birth_date,
                'is_primary' => true,
                'selection_type_id' => $selectionBorrowerType?->id,
                'type' => $selectionBorrowerType?->value,
                'selection_gender_id' => $selectionBorrowerGender?->id,
                'gender' => $selectionBorrowerGender?->value,
                'selection_race_id' => $selectionBorrowerRace?->id,
                'race' => $selectionBorrowerRace?->value,
                'selection_nationality_id' => $selectionBorrowerNationality?->id,
                'nationality' => $selectionBorrowerNationality?->value,
                'selection_education_level_id' => $selectionBorrowerEducationLevel?->id,
                'education_level' => $selectionBorrowerEducationLevel?->value,
                'selection_marriage_status_id' => $selectionBorrowerMarriageStatus?->id,
                'marriage_status' => $selectionBorrowerMarriageStatus?->value,
                'remark' => $borrower->remark,
            ]);

            $borrowerEmployment = $borrower->employment;
            $borrowerCompany = $borrower->company;

            if ($borrower->selection_type_id == 28) {
                $selectionTermsOfEmployment = Selection::find($borrowerEmployment->selection_terms_of_employment_id);
                $selectionOccupation = Selection::find($borrowerEmployment->selection_occupation_id);
                $selectionBusinessCategory = Selection::find($borrowerEmployment->selection_business_category_id);

                $loanBorrowerEmployment = $loanBorrowerProfile->loanCustomerEmployment()->create([
                    'employer_name' => $borrowerEmployment->employer_name,
                    'length_service_year' => $borrowerEmployment->length_service_year,
                    'length_service_month' => $borrowerEmployment->length_service_month,
                    'job_position' => $borrowerEmployment->job_position,
                    'selection_terms_id' => $selectionTermsOfEmployment?->id,
                    'terms' => $selectionTermsOfEmployment?->value,
                    'selection_occupation_id' => $selectionOccupation?->id,
                    'occupation' => $selectionOccupation?->value,
                    'selection_business_category_id' => $selectionBusinessCategory?->id,
                    'business_category' => $selectionBusinessCategory?->value,
                    'gross_income' => $borrowerEmployment->gross_income,
                    'net_income' => $borrowerEmployment->net_income,
                ]);

                // Create loan customer employment address
                $borrowerEmploymentAddress = $borrowerEmployment->address;

                $selectionEmploymentAddressType = Selection::find($borrowerEmploymentAddress->selection_type_id);
                $selectionEmploymentAddressState = Selection::find($borrowerEmploymentAddress->selection_state_id);
                $selectionEmploymentAddressCountry = Selection::find($borrowerEmploymentAddress->selection_country_id);

                $loanBorrowerEmployment->address()->create([
                    'category' => $borrowerEmploymentAddress->category,
                    'selection_type_id' => $selectionEmploymentAddressType?->id,
                    'type' => $selectionEmploymentAddressType?->value,
                    'line_1' => $borrowerEmploymentAddress->line_1,
                    'line_2' => $borrowerEmploymentAddress->line_2,
                    'postcode' => $borrowerEmploymentAddress->postcode,
                    'city' => $borrowerEmploymentAddress->city,
                    'selection_state_id' => $selectionEmploymentAddressState?->id,
                    'state' => $selectionEmploymentAddressState?->value,
                    'selection_country_id' => $selectionEmploymentAddressCountry?->id,
                    'country' => $selectionEmploymentAddressCountry?->value,
                    'is_primary' => $borrowerEmploymentAddress->is_primary,
                ]);

                // Create loan customer employment contacts
                $borrowerEmploymentContacts = $borrowerEmployment->contacts;
                foreach ($borrowerEmploymentContacts as $borrowerEmploymentContact) {
                    $selectionEmploymentContactType = Selection::find($borrowerEmploymentContact->selection_type_id);
                    $selectionEmploymentContactCountry = Selection::find($borrowerEmploymentContact->selection_country_id);

                    $loanBorrowerEmployment->contacts()->create([
                        'category' => $borrowerEmploymentContact->category,
                        'selection_type_id' => $selectionEmploymentContactType?->id,
                        'type' => $selectionEmploymentContactType?->value,
                        'selection_country_id' => $selectionEmploymentContactCountry?->id,
                        'country' => $selectionEmploymentContactCountry?->value,
                        'contact' => $borrowerEmploymentContact->contact,
                        'is_primary' => $borrowerEmploymentContact->is_primary,
                        'can_receive_sms' => $borrowerEmploymentContact->can_receive_sms,
                    ]);
                }
            } elseif ($borrower->selection_type_id == 29) {
                $selectionNatureOfBusiness = Selection::find($borrowerCompany->selection_nature_of_business_id);
                $selectionCountryOfBusiness = Selection::find($borrowerCompany->selection_country_of_business_id);

                $loanBorrowerCompany = $loanBorrowerProfile->loanCustomerCompany()->create([
                    'current_paid_up_capital' => $borrowerCompany->current_paid_up_capital,
                    'business_turnover' => $borrowerCompany->business_turnover,
                    'business_turnover_date' => $borrowerCompany->business_turnover_date,
                    'business_net_income' => $borrowerCompany->business_net_income,
                    'business_net_income_date' => $borrowerCompany->business_net_income_date,
                    'selection_nature_of_business_id' => $selectionNatureOfBusiness?->id,
                    'nature_of_business' => $selectionNatureOfBusiness?->value,
                    'selection_country_of_business_id' => $selectionCountryOfBusiness?->id,
                    'country_of_business' => $selectionCountryOfBusiness?->value,
                ]);

                // Create loan customer company owners
                $borrowerCompanyOwners = $borrowerCompany->owners;
                foreach ($borrowerCompanyOwners as $borrowerCompanyOwner) {
                    $selectionOwnerType = Selection::find($borrowerCompanyOwner->selection_type_id);
                    $selectionOwnerNationality = Selection::find($borrowerCompanyOwner->selection_nationality_id);

                    $loanBorrowerCompany->owners()->create([
                        'name' => $borrowerCompanyOwner->name,
                        'identity_no' => $borrowerCompanyOwner->identity_no,
                        'selection_type_id' => $selectionOwnerType?->id,
                        'type' => $selectionOwnerType?->value,
                        'selection_nationality_id' => $selectionOwnerNationality?->id,
                        'nationality' => $selectionOwnerNationality?->value,
                        'share_unit' => $borrowerCompanyOwner->share_unit,
                    ]);
                }
            }

            // Create loan customer addresses
            $loanBorrowerAddresses = $loanBorrowerProfile->loanCustomerAddresses()->create();

            $borrowerCustomerAddresses = $borrower->customerAddresses;
            foreach ($borrowerCustomerAddresses as $borrowerCustomerAddress) {
                $borrowerAddresses = $borrowerCustomerAddress->addresses;

                foreach ($borrowerAddresses as $borrowerAddress) {
                    $selectionAddressType = Selection::find($borrowerAddress->selection_type_id);
                    $selectionAddressState = Selection::find($borrowerAddress->selection_state_id);
                    $selectionAddressCountry = Selection::find($borrowerAddress->selection_country_id);

                    $loanBorrowerAddresses->addresses()->create([
                        'category' => $borrowerAddress->category,
                        'selection_type_id' => $selectionAddressType?->id,
                        'type' => $selectionAddressType?->value,
                        'line_1' => $borrowerAddress->line_1,
                        'line_2' => $borrowerAddress->line_2,
                        'postcode' => $borrowerAddress->postcode,
                        'city' => $borrowerAddress->city,
                        'selection_state_id' => $selectionAddressState?->id,
                        'state' => $selectionAddressState?->value,
                        'selection_country_id' => $selectionAddressCountry?->id,
                        'country' => $selectionAddressCountry?->value,
                        'is_primary' => true,
                    ]);
                }
            }

            // Create loan customer contacts
            $loanBorrowerContacts = $loanBorrowerProfile->loanCustomerContacts()->create();

            $borrowerCustomerContacts = $borrower->customerContacts;

            foreach ($borrowerCustomerContacts as $borrowerCustomerContact) {
                $borrowerContacts = $borrowerCustomerContact->contacts;
                foreach ($borrowerContacts as $borrowerContact) {
                    $selectionContactType = Selection::find($borrowerContact->selection_type_id);
                    $selectionContactCountry = Selection::find($borrowerContact->selection_country_id);

                    $loanBorrowerContacts->contacts()->create([
                        'category' => $borrowerContact->category,
                        'selection_type_id' => $selectionContactType?->id,
                        'type' => $selectionContactType?->value,
                        'selection_country_id' => $selectionContactCountry?->id,
                        'country' => $selectionContactCountry?->value,
                        'contact' => $borrowerContact->contact,
                        'is_primary' => $borrowerContact->is_primary,
                        'can_receive_sms' => $borrowerContact->can_receive_sms,
                    ]);
                }
            }

            // Create co-borrowers
            $coBorrowerIds = $request->input('co_borrower.ids', []);
            foreach ($coBorrowerIds as $coBorrowerId) {
                $coBorrower = CustomerProfile::with([
                    'employment.address', 'employment.contacts',
                    'company.owners',
                    'customerAddresses.addresses', 'customerContacts.contacts',
                ])->find($coBorrowerId);

                $selectionCoBorrowerType = Selection::find($coBorrower->selection_type_id);
                $selectionCoBorrowerGender = Selection::find($coBorrower->selection_gender_id);
                $selectionCoBorrowerRace = Selection::find($coBorrower->selection_race_id);
                $selectionCoBorrowerNationality = Selection::find($coBorrower->selection_nationality_id);
                $selectionCoBorrowerEducationLevel = Selection::find($coBorrower->selection_education_level_id);
                $selectionCoBorrowerMarriageStatus = Selection::find($coBorrower->selection_marriage_status_id);

                // Create co-borrower profile
                $loanCoBorrowerProfile = $loan->loanCustomerProfiles()->create([
                    'customer_id' => $coBorrowerId,
                    'code' => $coBorrower->code,
                    'name' => $coBorrower->name,
                    'email' => $coBorrower->email,
                    'identity_no' => $coBorrower->identity_no,
                    'old_identity_no' => $coBorrower->old_identity_no,
                    'registration_date' => $coBorrower->registration_date,
                    'years_of_incorporation' => $coBorrower->years_of_incorporation,
                    'age' => $coBorrower->age,
                    'birth_date' => $coBorrower->birth_date,
                    'is_primary' => false,
                    'selection_type_id' => $selectionCoBorrowerType?->id,
                    'type' => $selectionCoBorrowerType?->value,
                    'selection_gender_id' => $selectionCoBorrowerGender?->id,
                    'gender' => $selectionCoBorrowerGender?->value,
                    'selection_race_id' => $selectionCoBorrowerRace?->id,
                    'race' => $selectionCoBorrowerRace?->value,
                    'selection_nationality_id' => $selectionCoBorrowerNationality?->id,
                    'nationality' => $selectionCoBorrowerNationality?->value,
                    'selection_education_level_id' => $selectionCoBorrowerEducationLevel?->id,
                    'education_level' => $selectionCoBorrowerEducationLevel?->value,
                    'selection_marriage_status_id' => $selectionCoBorrowerMarriageStatus?->id,
                    'marriage_status' => $selectionCoBorrowerMarriageStatus?->value,
                    'remark' => $coBorrower->remark,
                ]);

                $coBorrowerEmployment = $coBorrower->employment;
                $coBorrowerCompany = $coBorrower->company;

                if ($coBorrower->selection_type_id == 28) {
                    $selectionTermsOfEmployment = Selection::find($coBorrowerEmployment->selection_terms_of_employment_id);
                    $selectionOccupation = Selection::find($coBorrowerEmployment->selection_occupation_id);
                    $selectionBusinessCategory = Selection::find($coBorrowerEmployment->selection_business_category_id);

                    $loanCoBorrowerEmployment = $loanCoBorrowerProfile->loanCustomerEmployment()->create([
                        'id' => $coBorrowerEmployment->id,
                        'employer_name' => $coBorrowerEmployment->employer_name,
                        'length_service_year' => $coBorrowerEmployment->length_service_year,
                        'length_service_month' => $coBorrowerEmployment->length_service_month,
                        'job_position' => $coBorrowerEmployment->job_position,
                        'selection_terms_id' => $selectionTermsOfEmployment?->id,
                        'terms' => $selectionTermsOfEmployment?->value,
                        'selection_occupation_id' => $selectionOccupation?->id,
                        'occupation' => $selectionOccupation?->value,
                        'selection_business_category_id' => $selectionBusinessCategory?->id,
                        'business_category' => $selectionBusinessCategory?->value,
                        'gross_income' => $coBorrowerEmployment->gross_income,
                        'net_income' => $coBorrowerEmployment->net_income,
                    ]);

                    $coBorrowerEmploymentAddress = $coBorrowerEmployment->address;
                    $selectionEmploymentAddressType = Selection::find($coBorrowerEmploymentAddress->selection_type_id);
                    $selectionEmploymentAddressState = Selection::find($coBorrowerEmploymentAddress->selection_state_id);
                    $selectionEmploymentAddressCountry = Selection::find($coBorrowerEmploymentAddress->selection_country_id);

                    $loanCoBorrowerEmployment->address()->create([
                        'category' => $coBorrowerEmploymentAddress->category,
                        'selection_type_id' => $selectionEmploymentAddressType?->id,
                        'type' => $selectionEmploymentAddressType?->value,
                        'line_1' => $coBorrowerEmploymentAddress->line_1,
                        'line_2' => $coBorrowerEmploymentAddress->line_2,
                        'postcode' => $coBorrowerEmploymentAddress->postcode,
                        'city' => $coBorrowerEmploymentAddress->city,
                        'selection_state_id' => $selectionEmploymentAddressState?->id,
                        'state' => $selectionEmploymentAddressState?->value,
                    ]);

                    $coBorrowerEmploymentContacts = $coBorrowerEmployment->contacts;
                    foreach ($coBorrowerEmploymentContacts as $coBorrowerEmploymentContact) {
                        $selectionEmploymentContactType = Selection::find($coBorrowerEmploymentContact->selection_type_id);
                        $selectionEmploymentContactCountry = Selection::find($coBorrowerEmploymentContact->selection_country_id);

                        $loanCoBorrowerEmployment->contacts()->create([
                            'category' => $coBorrowerEmploymentContact->category,
                            'selection_type_id' => $selectionEmploymentContactType?->id,
                            'type' => $selectionEmploymentContactType?->value,
                            'selection_country_id' => $selectionEmploymentContactCountry?->id,
                            'country' => $selectionEmploymentContactCountry?->value,
                            'contact' => $coBorrowerEmploymentContact->contact,
                            'is_primary' => $coBorrowerEmploymentContact->is_primary,
                            'can_receive_sms' => $coBorrowerEmploymentContact->can_receive_sms,
                        ]);
                    }
                } elseif ($coBorrower->selection_type_id == 29) {
                    $selectionNatureOfBusiness = Selection::find($coBorrowerCompany->selection_nature_of_business_id);
                    $selectionCountryOfBusiness = Selection::find($coBorrowerCompany->selection_country_of_business_id);

                    $loanCoBorrowerCompany = $loanCoBorrowerProfile->loanCustomerCompany()->create([
                        'current_paid_up_capital' => $coBorrowerCompany->current_paid_up_capital,
                        'business_turnover' => $coBorrowerCompany->business_turnover,
                        'business_turnover_date' => $coBorrowerCompany->business_turnover_date,
                        'selection_nature_of_business_id' => $selectionNatureOfBusiness?->id,
                        'nature_of_business' => $selectionNatureOfBusiness?->value,
                        'selection_country_of_business_id' => $selectionCountryOfBusiness?->id,
                        'country_of_business' => $selectionCountryOfBusiness?->value,
                    ]);

                    $coBorrowerCompanyOwners = $coBorrowerCompany->owners;
                    foreach ($coBorrowerCompanyOwners as $coBorrowerCompanyOwner) {
                        $selectionOwnerType = Selection::find($coBorrowerCompanyOwner->selection_type_id);
                        $selectionOwnerNationality = Selection::find($coBorrowerCompanyOwner->selection_nationality_id);

                        $loanCoBorrowerCompany->owners()->create([
                            'name' => $coBorrowerCompanyOwner->name,
                            'identity_no' => $coBorrowerCompanyOwner->identity_no,
                            'selection_type_id' => $selectionOwnerType?->id,
                            'type' => $selectionOwnerType?->value,
                            'selection_nationality_id' => $selectionOwnerNationality?->id,
                            'nationality' => $selectionOwnerNationality?->value,
                            'share_unit' => $coBorrowerCompanyOwner->share_unit,
                        ]);
                    }
                }

                // Create co-borrower addresses
                $loanCoBorrowerAddresses = $loanCoBorrowerProfile->loanCustomerAddresses()->create();

                $coBorrowerCustomerAddresses = $coBorrower->customerAddresses;
                foreach ($coBorrowerCustomerAddresses as $coBorrowerCustomerAddress) {
                    $coBorrowerAddresses = $coBorrowerCustomerAddress->addresses;

                    foreach ($coBorrowerAddresses as $coBorrowerAddress) {
                        $selectionAddressType = Selection::find($coBorrowerAddress->selection_type_id);
                        $selectionAddressState = Selection::find($coBorrowerAddress->selection_state_id);
                        $selectionAddressCountry = Selection::find($coBorrowerAddress->selection_country_id);

                        $loanCoBorrowerAddresses->addresses()->create([
                            'category' => $coBorrowerAddress->category,
                            'selection_type_id' => $selectionAddressType?->id,
                            'type' => $selectionAddressType?->value,
                            'line_1' => $coBorrowerAddress->line_1,
                            'line_2' => $coBorrowerAddress->line_2,
                            'postcode' => $coBorrowerAddress->postcode,
                            'city' => $coBorrowerAddress->city,
                            'selection_state_id' => $selectionAddressState?->id,
                            'state' => $selectionAddressState?->value,
                            'selection_country_id' => $selectionAddressCountry?->id,
                            'country' => $selectionAddressCountry?->value,
                            'is_primary' => true,
                        ]);
                    }
                }

                // Create co-borrower contacts
                $loanCoBorrowerContacts = $loanCoBorrowerProfile->loanCustomerContacts()->create();

                $coBorrowerCustomerContacts = $coBorrower->customerContacts;

                foreach ($coBorrowerCustomerContacts as $coBorrowerCustomerContact) {
                    $coBorrowerContacts = $coBorrowerCustomerContact->contacts;
                    foreach ($coBorrowerContacts as $coBorrowerContact) {
                        $selectionContactType = Selection::find($coBorrowerContact->selection_type_id);
                        $selectionContactCountry = Selection::find($coBorrowerContact->selection_country_id);

                        $loanCoBorrowerContacts->contacts()->create([
                            'category' => $coBorrowerContact->category,
                            'selection_type_id' => $selectionContactType?->id,
                            'type' => $selectionContactType?->value,
                            'selection_country_id' => $selectionContactCountry?->id,
                            'country' => $selectionContactCountry?->value,
                            'contact' => $coBorrowerContact->contact,
                            'is_primary' => $coBorrowerContact->is_primary,
                            'can_receive_sms' => $coBorrowerContact->can_receive_sms,
                        ]);
                    }
                }
            }

            // Create collaterals
            $collateralIds = $request->input('collateral.ids', []);
            foreach ($collateralIds as $collateralId) {
                $loanCustomerCollateral = $loan->loanCustomerCollaterals()->create([
                    'customer_collateral_id' => $collateralId,
                ]);

                $customerCollateral = CustomerCollateral::with(
                    'collateral.property.propertyOwners.address', 'collateral.property.propertyOwners.contacts',
                    'collateral.property.address', 'collateral.valuers',
                )->find($collateralId);

                // Duplicate the collateral
                $originalCollateral = $customerCollateral->collateral;

                $selectionCollateralCustomerType = Selection::find($originalCollateral->selection_customer_type_id);
                $selectionCollateralType = Selection::find($originalCollateral->selection_type_id);

                $newCollateral = $originalCollateral->replicate();
                $newCollateral->uuid = null;
                $newCollateral->loan_customer_collateral_id = $loanCustomerCollateral->id;
                $newCollateral->customer_type = $selectionCollateralCustomerType?->value;
                $newCollateral->type = $selectionCollateralType?->value;
                $newCollateral->code = null;
                $newCollateral->save();

                // Duplicate the property
                $originalProperty = $customerCollateral->collateral->property;

                $selectionPropertyLandCategory = Selection::find($originalProperty->selection_land_category_id);
                $selectionPropertyTypeOfProperty = Selection::find($originalProperty->selection_type_of_property_id);
                $selectionPropertyLandSizeUnit = Selection::find($originalProperty->selection_land_size_unit);
                $selectionPropertyLandStatus = Selection::find($originalProperty->selection_land_status_id);
                $selectionPropertyBuiltUpAreaUnit = Selection::find($originalProperty->selection_built_up_area_unit);

                $newProperty = $originalProperty->replicate();
                $newProperty->uuid = null;
                $newProperty->collateral_id = $newCollateral->id;
                $newProperty->land_category = $selectionPropertyLandCategory?->value;
                $newProperty->type_of_property = $selectionPropertyTypeOfProperty?->value;
                $newProperty->land_size_unit = $selectionPropertyLandSizeUnit?->value;
                $newProperty->land_status = $selectionPropertyLandStatus?->value;
                $newProperty->built_up_area_unit = $selectionPropertyBuiltUpAreaUnit?->value;
                $newProperty->save();

                // Duplicate the property address
                $originalPropertyAddress = $customerCollateral->collateral->property->address;

                $selectionPropertyAddressType = Selection::find($originalPropertyAddress->selection_type_id);
                $selectionPropertyAddressState = Selection::find($originalPropertyAddress->selection_state_id);
                $selectionPropertyAddressCountry = Selection::find($originalPropertyAddress->selection_country_id);

                $newPropertyAddress = $originalPropertyAddress->replicate();
                $newPropertyAddress->uuid = null;
                $newPropertyAddress->addressable_id = $newProperty->id;
                $newPropertyAddress->addressable_type = CollateralProperty::class;
                $newPropertyAddress->type = $selectionPropertyAddressType?->value;
                $newPropertyAddress->state = $selectionPropertyAddressState?->value;
                $newPropertyAddress->country = $selectionPropertyAddressCountry?->value;
                $newPropertyAddress->save();

                // Duplicate the property owners
                $originalPropertyOwners = $customerCollateral->collateral->property->propertyOwners;
                foreach ($originalPropertyOwners as $originalPropertyOwner) {
                    $newPropertyOwner = $originalPropertyOwner->replicate();
                    $newPropertyOwner->uuid = null;
                    $newPropertyOwner->collateral_property_id = $newProperty->id;
                    $newPropertyOwner->save();

                    // Duplicate the property owner address
                    $originalPropertyOwnerAddress = $originalPropertyOwner->address;

                    $selectionPropertyOwnerAddressType = Selection::find($originalPropertyOwnerAddress->selection_type_id);
                    $selectionPropertyOwnerAddressState = Selection::find($originalPropertyOwnerAddress->selection_state_id);
                    $selectionPropertyOwnerAddressCountry = Selection::find($originalPropertyOwnerAddress->selection_country_id);

                    $newPropertyOwnerAddress = $originalPropertyOwnerAddress->replicate();
                    $newPropertyOwnerAddress->uuid = null;
                    $newPropertyOwnerAddress->addressable_id = $newPropertyOwner->id;
                    $newPropertyOwnerAddress->addressable_type = CollateralPropertyOwner::class;
                    $newPropertyOwnerAddress->type = $selectionPropertyOwnerAddressType?->value;
                    $newPropertyOwnerAddress->state = $selectionPropertyOwnerAddressState?->value;
                    $newPropertyOwnerAddress->country = $selectionPropertyOwnerAddressCountry?->value;
                    $newPropertyOwnerAddress->save();

                    // Duplicate the property owner contacts
                    $originalPropertyOwnerContacts = $originalPropertyOwner->contacts;
                    foreach ($originalPropertyOwnerContacts as $originalPropertyOwnerContact) {
                        $selectionPropertyOwnerContactType = Selection::find($originalPropertyOwnerContact->selection_type_id);
                        $selectionPropertyOwnerContactCountry = Selection::find($originalPropertyOwnerContact->selection_country_id);

                        $newPropertyOwnerContact = $originalPropertyOwnerContact->replicate();
                        $newPropertyOwnerContact->uuid = null;
                        $newPropertyOwnerContact->contactable_id = $newPropertyOwner->id;
                        $newPropertyOwnerContact->contactable_type = CollateralPropertyOwner::class;
                        $newPropertyOwnerContact->type = $selectionPropertyOwnerContactType?->value;
                        $newPropertyOwnerContact->country = $selectionPropertyOwnerContactCountry?->value;
                        $newPropertyOwnerContact->save();
                    }
                }

                // Duplicate the valuers
                $originalValuers = $customerCollateral->collateral->valuers;
                foreach ($originalValuers as $originalValuer) {
                    $newValuer = $originalValuer->replicate();
                    $newValuer->uuid = null;
                    $newValuer->collateral_id = $newCollateral->id;
                    $newValuer->save();
                }
            }

            // Create emergency contact
            $emergency = $request->input('emergency');
            if ($emergency) {
                $emergencyInfo = $loan->loanEmergencies()->create([
                    'name' => $emergency['personal']['name'] ?? null,
                    'identity_no' => $emergency['personal']['identity_no'] ?? null,
                    'birth_date' => $emergency['personal']['birth_date'] ?? null,
                    'age' => $emergency['personal']['age'] ?? null,
                    'selection_gender_id' => $emergency['personal']['selection_gender_id'] ?? null,
                    'selection_relationship_id' => $emergency['personal']['selection_relationship_id'] ?? null,
                    'selection_nationality_id' => $emergency['personal']['selection_nationality_id'] ?? null,
                    'employment_name' => $emergency['employment']['employment_name'] ?? null,
                ]);

                // Personal Information
                $emergencyPersonalDetail = $emergencyInfo->loanEmergencyDetails()->create([
                    'type' => 0,
                ]);

                $emergencyPersonalDetail->address()->create([
                    'selection_state_id' => $emergency['personal']['address']['selection_state_id'] ?? null,
                    'selection_country_id' => $emergency['personal']['address']['selection_country_id'] ?? null,
                    'line_1' => $emergency['personal']['address']['line_1'] ?? null,
                    'line_2' => $emergency['personal']['address']['line_2'] ?? null,
                    'postcode' => $emergency['personal']['address']['postcode'] ?? null,
                    'city' => $emergency['personal']['address']['city'] ?? null,
                    'is_primary' => true,
                ]);

                $emergencyPersonalDetail->contact()->createMany([
                    [
                        'selection_type_id' => Selection::where('category', 'contact_type')->where('value', 'Telephone')->value('id'),
                        'category' => Contact::CATEGORY_TELEPHONE,
                        'selection_country_id' => $emergency['personal']['contact']['selection_telephone_country_id'] ?? null,
                        'contact' => $emergency['personal']['contact']['telephone'] ?? null,
                        'can_receive_sms' => null,
                    ],
                    [
                        'selection_type_id' => Selection::where('category', 'contact_type')->where('value', 'Mobile Phone')->value('id'),
                        'category' => Contact::CATEGORY_MOBILE,
                        'selection_country_id' => $emergency['personal']['contact']['selection_mobile_country_id'] ?? null,
                        'contact' => $emergency['personal']['contact']['mobile_phone'] ?? null,
                        'can_receive_sms' => $emergency['personal']['contact']['can_receive_sms'] ?? null,
                    ],
                ]);

                // Employment Information
                $emergencyEmploymentDetail = $emergencyInfo->loanEmergencyDetails()->create([
                    'type' => 1,
                ]);

                $emergencyEmploymentDetail->address()->create([
                    'selection_state_id' => $emergency['employment']['address']['selection_state_id'] ?? null,
                    'selection_country_id' => $emergency['employment']['address']['selection_country_id'] ?? null,
                    'line_1' => $emergency['employment']['address']['line_1'] ?? null,
                    'line_2' => $emergency['employment']['address']['line_2'] ?? null,
                    'postcode' => $emergency['employment']['address']['postcode'] ?? null,
                    'city' => $emergency['employment']['address']['city'] ?? null,
                    'is_primary' => true,
                ]);

                $emergencyEmploymentDetail->contact()->createMany([
                    [
                        'selection_type_id' => Selection::where('category', 'contact_type')->where('value', 'Telephone')->value('id'),
                        'category' => Contact::CATEGORY_TELEPHONE,
                        'selection_country_id' => $emergency['employment']['contact']['selection_telephone_country_id'] ?? null,
                        'contact' => $emergency['employment']['contact']['telephone'] ?? null,
                        'can_receive_sms' => null,
                    ],
                    [
                        'selection_type_id' => Selection::where('category', 'contact_type')->where('value', 'Mobile Phone')->value('id'),
                        'category' => Contact::CATEGORY_MOBILE,
                        'selection_country_id' => $emergency['employment']['contact']['selection_mobile_country_id'] ?? null,
                        'contact' => $emergency['employment']['contact']['mobile_phone'] ?? null,
                        'can_receive_sms' => $emergency['employment']['contact']['can_receive_sms'] ?? null,
                    ],
                ]);
            }

            // Create guarantors
            $guarantors = $request->input('guarantor', []);
            foreach ($guarantors as $guarantor) {
                $selectionGender = Selection::find($guarantor['personal']['selection_gender_id']);
                $selectionRelationship = Selection::find($guarantor['personal']['selection_relationship_id']);
                $selectionNationality = Selection::find($guarantor['personal']['selection_nationality_id']);
                $selectionTermsOfEmployment = Selection::find($guarantor['employment']['selection_terms_of_employment_id']);
                $selectionOccupation = Selection::find($guarantor['employment']['selection_occupation_id']);
                $selectionBusinessClassification = Selection::find($guarantor['employment']['selection_business_classification_id']);

                $loanGuarantor = $loan->loanGuarantors()->create([
                    'name' => $guarantor['personal']['name'] ?? null,
                    'identity_no' => $guarantor['personal']['identity_no'] ?? null,
                    'birth_date' => $guarantor['personal']['birth_date'] ?? null,
                    'age' => $guarantor['personal']['age'] ?? null,
                    'selection_gender_id' => $selectionGender?->id,
                    'gender' => $selectionGender?->value,
                    'selection_relationship_id' => $selectionRelationship?->id,
                    'relationship' => $selectionRelationship?->value,
                    'selection_nationality_id' => $selectionNationality?->id,
                    'nationality' => $selectionNationality?->value,
                    'employment_name' => $guarantor['employment']['employment_name'] ?? null,
                    'length_service_year' => $guarantor['employment']['length_service_year'] ?? null,
                    'length_service_month' => $guarantor['employment']['length_service_month'] ?? null,
                    'job_position' => $guarantor['employment']['job_position'] ?? null,
                    'selection_terms_of_employment_id' => $selectionTermsOfEmployment?->id,
                    'terms_of_employment' => $selectionTermsOfEmployment?->value,
                    'selection_occupation_id' => $selectionOccupation?->id,
                    'occupation' => $selectionOccupation?->value,
                    'selection_business_classification_id' => $selectionBusinessClassification?->id,
                    'business_classification' => $selectionBusinessClassification?->value,
                ]);

                // Personal Information
                $loanGuarantorPersonalDetail = $loanGuarantor->loanGuarantorDetails()->create([
                    'type' => 0,
                ]);

                $loanGuarantorPersonalDetail->address()->create([
                    'selection_state_id' => $guarantor['personal']['address']['selection_state_id'] ?? null,
                    'selection_country_id' => $guarantor['personal']['address']['selection_country_id'] ?? null,
                    'line_1' => $guarantor['personal']['address']['line_1'] ?? null,
                    'line_2' => $guarantor['personal']['address']['line_2'] ?? null,
                    'postcode' => $guarantor['personal']['address']['postcode'] ?? null,
                    'city' => $guarantor['personal']['address']['city'] ?? null,
                    'is_primary' => true,
                ]);

                $loanGuarantorPersonalDetail->contact()->createMany([
                    [
                        'selection_type_id' => Selection::where('category', 'contact_type')->where('value', 'Telephone')->value('id'),
                        'category' => Contact::CATEGORY_TELEPHONE,
                        'selection_country_id' => $guarantor['personal']['contact']['selection_telephone_country_id'] ?? null,
                        'contact' => $guarantor['personal']['contact']['telephone'] ?? null,
                        'can_receive_sms' => null,
                    ],
                    [
                        'selection_type_id' => Selection::where('category', 'contact_type')->where('value', 'Mobile Phone')->value('id'),
                        'category' => Contact::CATEGORY_MOBILE,
                        'selection_country_id' => $guarantor['personal']['contact']['selection_mobile_country_id'] ?? null,
                        'contact' => $guarantor['personal']['contact']['mobile_phone'] ?? null,
                        'can_receive_sms' => $guarantor['personal']['contact']['can_receive_sms'] ?? null,
                    ],
                ]);

                // Employment Information
                $loanGuarantorEmploymentDetail = $loanGuarantor->loanGuarantorDetails()->create([
                    'type' => 1,
                ]);

                $loanGuarantorEmploymentDetail->address()->create([
                    'selection_state_id' => $guarantor['employment']['address']['selection_state_id'] ?? null,
                    'selection_country_id' => $guarantor['employment']['address']['selection_country_id'] ?? null,
                    'line_1' => $guarantor['employment']['address']['line_1'] ?? null,
                    'line_2' => $guarantor['employment']['address']['line_2'] ?? null,
                    'postcode' => $guarantor['employment']['address']['postcode'] ?? null,
                    'city' => $guarantor['employment']['address']['city'] ?? null,
                    'is_primary' => true,
                ]);

                $loanGuarantorEmploymentDetail->contact()->createMany([
                    [
                        'selection_type_id' => Selection::where('category', 'contact_type')->where('value', 'Telephone')->value('id'),
                        'category' => Contact::CATEGORY_TELEPHONE,
                        'selection_country_id' => $guarantor['employment']['contact']['selection_telephone_country_id'] ?? null,
                        'contact' => $guarantor['employment']['contact']['telephone'] ?? null,
                        'can_receive_sms' => null,
                    ],
                    [
                        'selection_type_id' => Selection::where('category', 'contact_type')->where('value', 'Mobile Phone')->value('id'),
                        'category' => Contact::CATEGORY_MOBILE,
                        'selection_country_id' => $guarantor['employment']['contact']['selection_mobile_country_id'] ?? null,
                        'contact' => $guarantor['employment']['contact']['mobile_phone'] ?? null,
                        'can_receive_sms' => $guarantor['employment']['contact']['can_receive_sms'] ?? null,
                    ],
                ]);
            }

            // Create documents
            $documents = $request->input('document', []);
            foreach ($documents as $doc) {
                $document = CustomerDocument::find($doc['id']);
                $selectionDocumentType = Selection::find($document->selection_type_id);

                $loanDocument = $loan->loanDocuments()->create([
                    'selection_type_id' => $selectionDocumentType?->id,
                    'type' => $selectionDocumentType?->value,
                ]);

                $doc = $document->documents()->first();

                $loanDocument->documents()->create([
                    'category' => $doc->category,
                    'url' => $doc->url,
                ]);
            }

            DB::commit();

            return Redirect::route('loans.index')->with('success', 'Loan created successfully.');
        } catch (\Exception $e) {
            DB::rollBack();
            dd($e->getMessage());

            return Redirect::back()->with('error', 'Failed to create loan: '.$e->getMessage())->withInput();
        }
    }

    /**
     * Display the specified loan.
     */
    public function show(Loan $loan): Response
    {
        $this->authorize('view', $loan);

        $loan->withAuditUsers();
        $loan->load([
            'company:id,code,display_name',
            'team:id,name',
            'agent:id,name',
            'selectionType:id,value',
        ]);

        return Inertia::render('loans/Show', [
            'loan' => (new LoanDetailResource($loan))->toArray(request(), true),
            'collateralTypes' => $this->getSelectionsOptionsForCategory('collateral_type'),
            'documentTypes' => $this->getSelectionsOptionsForCategory('document_type'),
            'transactionTypes' => $this->getSelectionsOptionsForCategory('transaction_type'),
            'letterTypes' => $this->getSelectionsOptionsForCategory('letter_type'),
        ]);
    }

    /**
     * Show the form for editing the specified loan.
     */
    public function edit(Loan $loan): Response
    {
        $this->authorize('update', $loan);

        return Inertia::render('loans/Edit', [
            'loan' => (new LoanDetailResource($loan))->toArray(request(), true),
            'loanTypes' => $this->getSelectionsOptionsForCategory('loan_type'),
            'loanModes' => $this->getSelectionsOptionsForCategory('loan_mode'),
            'repaymentMethods' => $this->getSelectionsOptionsForCategory('repayment_method'),
            'relationships' => $this->getSelectionsOptionsForCategory('relationship'),
            'telephoneCountries' => $this->getSelectionsOptionsForCategory('telephone_country'),
            'mobileCountries' => $this->getSelectionsOptionsForCategory('mobile_country'),
            'genderTypes' => $this->getSelectionsOptionsForCategory('gender'),
            'contactTypes' => $this->getSelectionsOptionsForCategory('contact_type'),
            'states' => $this->getSelectionsOptionsForCategory('state'),
            'countries' => $this->getSelectionsOptionsForCategory('country'),
            'nationalities' => $this->getSelectionsOptionsForCategory('nationality'),
            'employmentTerms' => $this->getSelectionsOptionsForCategory('terms_of_employment'),
            'businessClassifications' => $this->getSelectionsOptionsForCategory('business_classification'),
            'occupations' => $this->getSelectionsOptionsForCategory('occupation'),
            'documentTypes' => $this->getSelectionsOptionsForCategory('document_type'),
            'collateralTypes' => $this->getSelectionsOptionsForCategory('collateral_type'),
            'bankAccountTypes' => $this->getSelectionsOptionsForCategory('bank_account_type'),
            'bankTypes' => $this->getSelectionsOptionsForCategory('bank_type'),
        ]);
    }

    /**
     * Update the specified loan in storage.
     */
    public function update(UpdateLoanRequest $request, Loan $loan): RedirectResponse
    {
        $this->authorize('update', $loan);

        try {
            DB::beginTransaction();

            $selectionLoanType = Selection::find($request->selection_type_id);

            $loan->update([
                'status' => LoanStatus::from($request->status),
                'selection_type_id' => $selectionLoanType?->id,
                'type' => $selectionLoanType?->value,
                'remark' => $request->input('loan.rejection_reason'),
            ]);

            $selectionModeType = Selection::find($request->input('loan.selection_mode_type_id'));
            $selectionRepaymentMethod = Selection::find($request->input('loan.selection_repayment_method_id'));

            // Create loan detail
            $loan->loanDetail()->update([
                'selection_mode_type_id' => $selectionModeType?->id,
                'mode_type' => $selectionModeType?->value,
                'loan_principle_amount' => $request->input('loan.loan_principle_amount'),
                'no_of_instalment' => $request->input('loan.no_of_instalment'),
                'last_payment' => $request->input('loan.last_payment'),
                'selection_repayment_method_id' => $selectionRepaymentMethod?->id,
                'repayment_method' => $selectionRepaymentMethod?->value,
                'instalment_amount' => $request->input('loan.instalment_amount'),
                'interest' => $request->input('loan.interest'),
                'late_payment_charges' => $request->input('loan.late_payment_charges'),
                'commencement_date' => $request->input('loan.commencement_date'),
                'stamping_date' => $request->input('loan.stamping_date'),
                'next_due_date' => $request->input('loan.next_due_date'),
                'rebate' => $request->input('loan.rebate'),
                'stamp_duty' => $request->input('loan.stamp_duty'),
                'attestation_fee' => $request->input('loan.attestation_fee'),
                'legal_fee' => $request->input('loan.legal_fee'),
                'processing_fee' => $request->input('loan.processing_fee'),
                'misc_charges' => $request->input('loan.misc_charges'),
                'loan_disbursement_amount' => $request->input('loan.loan_disbursement_amount'),
            ]);

            if ($request->has('collateral')) {
                foreach ($request->collateral as $item) {
                    if (! empty($item['id']) && $item['_delete']) {
                        $loanCustomerCollateral = LoanCustomerCollateral::find($item['id']);
                        if ($loanCustomerCollateral) {
                            $loanCustomerCollateral->delete();
                        }

                        continue;
                    }

                    if (empty($item['id']) && ! $item['_delete'] && ! empty($item['collateral_id'])) {
                        $loanCustomerCollateral = $loan->loanCustomerCollaterals()->create([
                            'loan_id' => $loan->id,
                            'customer_collateral_id' => $item['collateral_id'],
                        ]);

                        $customerCollateral = CustomerCollateral::with(
                            'collateral.property.propertyOwners.address', 'collateral.property.propertyOwners.contacts',
                            'collateral.property.address', 'collateral.valuers',
                        )->find($item['collateral_id']);

                        // Duplicate the collateral
                        $originalCollateral = $customerCollateral->collateral;

                        $selectionCollateralCustomerType = Selection::find($originalCollateral->selection_customer_type_id);
                        $selectionCollateralType = Selection::find($originalCollateral->selection_type_id);

                        $newCollateral = $originalCollateral->replicate();
                        $newCollateral->uuid = null;
                        $newCollateral->loan_customer_collateral_id = $loanCustomerCollateral->id;
                        $newCollateral->customer_type = $selectionCollateralCustomerType?->value;
                        $newCollateral->type = $selectionCollateralType?->value;
                        $newCollateral->code = null;
                        $newCollateral->save();

                        // Duplicate the property
                        $originalProperty = $customerCollateral->collateral->property;

                        $selectionPropertyLandCategory = Selection::find($originalProperty->selection_land_category_id);
                        $selectionPropertyTypeOfProperty = Selection::find($originalProperty->selection_type_of_property_id);
                        $selectionPropertyLandSizeUnit = Selection::find($originalProperty->selection_land_size_unit);
                        $selectionPropertyLandStatus = Selection::find($originalProperty->selection_land_status_id);
                        $selectionPropertyBuiltUpAreaUnit = Selection::find($originalProperty->selection_built_up_area_unit);

                        $newProperty = $originalProperty->replicate();
                        $newProperty->uuid = null;
                        $newProperty->collateral_id = $newCollateral->id;
                        $newProperty->land_category = $selectionPropertyLandCategory?->value;
                        $newProperty->type_of_property = $selectionPropertyTypeOfProperty?->value;
                        $newProperty->land_size_unit = $selectionPropertyLandSizeUnit?->value;
                        $newProperty->land_status = $selectionPropertyLandStatus?->value;
                        $newProperty->built_up_area_unit = $selectionPropertyBuiltUpAreaUnit?->value;
                        $newProperty->save();

                        // Duplicate the property address
                        $originalPropertyAddress = $customerCollateral->collateral->property->address;

                        $selectionPropertyAddressType = Selection::find($originalPropertyAddress->selection_type_id);
                        $selectionPropertyAddressState = Selection::find($originalPropertyAddress->selection_state_id);
                        $selectionPropertyAddressCountry = Selection::find($originalPropertyAddress->selection_country_id);

                        $newPropertyAddress = $originalPropertyAddress->replicate();
                        $newPropertyAddress->uuid = null;
                        $newPropertyAddress->addressable_id = $newProperty->id;
                        $newPropertyAddress->addressable_type = CollateralProperty::class;
                        $newPropertyAddress->type = $selectionPropertyAddressType?->value;
                        $newPropertyAddress->state = $selectionPropertyAddressState?->value;
                        $newPropertyAddress->country = $selectionPropertyAddressCountry?->value;
                        $newPropertyAddress->save();

                        // Duplicate the property owners
                        $originalPropertyOwners = $customerCollateral->collateral->property->propertyOwners;
                        foreach ($originalPropertyOwners as $originalPropertyOwner) {
                            $newPropertyOwner = $originalPropertyOwner->replicate();
                            $newPropertyOwner->uuid = null;
                            $newPropertyOwner->collateral_property_id = $newProperty->id;
                            $newPropertyOwner->save();

                            // Duplicate the property owner address
                            $originalPropertyOwnerAddress = $originalPropertyOwner->address;

                            $selectionPropertyOwnerAddressType = Selection::find($originalPropertyOwnerAddress->selection_type_id);
                            $selectionPropertyOwnerAddressState = Selection::find($originalPropertyOwnerAddress->selection_state_id);
                            $selectionPropertyOwnerAddressCountry = Selection::find($originalPropertyOwnerAddress->selection_country_id);

                            $newPropertyOwnerAddress = $originalPropertyOwnerAddress->replicate();
                            $newPropertyOwnerAddress->uuid = null;
                            $newPropertyOwnerAddress->addressable_id = $newPropertyOwner->id;
                            $newPropertyOwnerAddress->addressable_type = CollateralPropertyOwner::class;
                            $newPropertyOwnerAddress->type = $selectionPropertyOwnerAddressType?->value;
                            $newPropertyOwnerAddress->state = $selectionPropertyOwnerAddressState?->value;
                            $newPropertyOwnerAddress->country = $selectionPropertyOwnerAddressCountry?->value;
                            $newPropertyOwnerAddress->save();

                            // Duplicate the property owner contacts
                            $originalPropertyOwnerContacts = $originalPropertyOwner->contacts;
                            foreach ($originalPropertyOwnerContacts as $originalPropertyOwnerContact) {
                                $selectionPropertyOwnerContactType = Selection::find($originalPropertyOwnerContact->selection_type_id);
                                $selectionPropertyOwnerContactCountry = Selection::find($originalPropertyOwnerContact->selection_country_id);

                                $newPropertyOwnerContact = $originalPropertyOwnerContact->replicate();
                                $newPropertyOwnerContact->uuid = null;
                                $newPropertyOwnerContact->contactable_id = $newPropertyOwner->id;
                                $newPropertyOwnerContact->contactable_type = CollateralPropertyOwner::class;
                                $newPropertyOwnerContact->type = $selectionPropertyOwnerContactType?->value;
                                $newPropertyOwnerContact->country = $selectionPropertyOwnerContactCountry?->value;
                                $newPropertyOwnerContact->save();
                            }
                        }

                        // Duplicate the valuers
                        $originalValuers = $customerCollateral->collateral->valuers;
                        foreach ($originalValuers as $originalValuer) {
                            $newValuer = $originalValuer->replicate();
                            $newValuer->uuid = null;
                            $newValuer->collateral_id = $newCollateral->id;
                            $newValuer->save();
                        }
                    }
                }
            }

            if ($request->has('emergency')) {
                $emergency = $request->input('emergency');
                $loanEmergency = LoanEmergency::findOrFail($emergency['id']);
                $loanEmergency->update([
                    'name' => $emergency['personal']['name'] ?? null,
                    'identity_no' => $emergency['personal']['identity_no'] ?? null,
                    'birth_date' => $emergency['personal']['birth_date'] ?? null,
                    'age' => $emergency['personal']['age'] ?? null,
                    'selection_gender_id' => $emergency['personal']['selection_gender_id'] ?? null,
                    'selection_relationship_id' => $emergency['personal']['selection_relationship_id'] ?? null,
                    'selection_nationality_id' => $emergency['personal']['selection_nationality_id'] ?? null,
                    'employment_name' => $emergency['employment']['employment_name'] ?? null,
                ]);

                $emergencyPersonalDetail = $loanEmergency->loanEmergencyDetails()->where('type', 0)->first();
                $emergencyPersonalContacts = $emergency['personal']['contact'] ?? [];
                $emergencyPersonalAddress = $emergency['personal']['address'] ?? [];

                $emergencyPersonalDetail->contact()->where('id', $emergencyPersonalContacts['telephone_id'])->update([
                    'contact' => $emergencyPersonalContacts['telephone'] ?? null,
                    'selection_country_id' => $emergencyPersonalContacts['selection_telephone_country_id'] ?? null,
                    'selection_type_id' => 33,
                    'type' => null,
                ]);

                $emergencyPersonalDetail->contact()->where('id', $emergencyPersonalContacts['mobile_id'])->update([
                    'contact' => $emergencyPersonalContacts['mobile_phone'] ?? null,
                    'selection_country_id' => $emergencyPersonalContacts['selection_mobile_country_id'] ?? null,
                    'selection_type_id' => 32,
                    'type' => null,
                ]);

                $emergencyPersonalDetail->address()->where('id', $emergencyPersonalAddress['id'])->update([
                    'line_1' => $emergencyPersonalAddress['line_1'] ?? null,
                    'line_2' => $emergencyPersonalAddress['line_2'] ?? null,
                    'postcode' => $emergencyPersonalAddress['postcode'] ?? null,
                    'city' => $emergencyPersonalAddress['city'] ?? null,
                    'selection_state_id' => $emergencyPersonalAddress['selection_state_id'] ?? null,
                    'selection_country_id' => $emergencyPersonalAddress['selection_country_id'] ?? null,
                ]);

                $emergencyEmploymentDetail = $loanEmergency->loanEmergencyDetails()->where('type', 1)->first();
                $emergencyEmploymentContacts = $emergency['employment']['contact'] ?? [];
                $emergencyEmploymentAddress = $emergency['employment']['address'] ?? [];

                $emergencyEmploymentDetail->contact()->where('id', $emergencyEmploymentContacts['telephone_id'])->update([
                    'contact' => $emergencyEmploymentContacts['telephone'] ?? null,
                    'selection_country_id' => $emergencyEmploymentContacts['selection_telephone_country_id'] ?? null,
                    'selection_type_id' => 33,
                    'type' => null,
                ]);

                $emergencyEmploymentDetail->contact()->where('id', $emergencyEmploymentContacts['mobile_id'])->update([
                    'contact' => $emergencyEmploymentContacts['mobile_phone'] ?? null,
                    'selection_country_id' => $emergencyEmploymentContacts['selection_mobile_country_id'] ?? null,
                    'selection_type_id' => 32,
                    'type' => null,
                ]);

                $emergencyEmploymentDetail->address()->where('id', $emergencyEmploymentAddress['id'])->update([
                    'line_1' => $emergencyEmploymentAddress['line_1'] ?? null,
                    'line_2' => $emergencyEmploymentAddress['line_2'] ?? null,
                    'postcode' => $emergencyEmploymentAddress['postcode'] ?? null,
                    'city' => $emergencyEmploymentAddress['city'] ?? null,
                    'selection_state_id' => $emergencyEmploymentAddress['selection_state_id'] ?? null,
                    'selection_country_id' => $emergencyEmploymentAddress['selection_country_id'] ?? null,
                ]);

            }

            if ($request->has('guarantors')) {
                $guarantors = $request->input('guarantors', []);

                foreach ($guarantors as $guarantorData) {
                    // Check if guarantor should be deleted
                    if ($guarantorData['_delete'] ?? false) {
                        if (isset($guarantorData['id'])) {
                            $loanGuarantor = LoanGuarantor::findOrFail($guarantorData['id']);
                            $loanGuarantor->delete();
                        }

                        continue;
                    }
                    // dd($request->all());
                    // Update or create guarantor
                    if (isset($guarantorData['id'])) {
                        // Update existing guarantor
                        $loanGuarantor = LoanGuarantor::findOrFail($guarantorData['id']);
                        $loanGuarantor->update([
                            'name' => $guarantorData['personal']['name'] ?? null,
                            'identity_no' => $guarantorData['personal']['identity_no'] ?? null,
                            'birth_date' => $guarantorData['personal']['birth_date'] ?? null,
                            'age' => $guarantorData['personal']['age'] ?? null,
                            'selection_gender_id' => $guarantorData['personal']['selection_gender_id'] ?? null,
                            'selection_relationship_id' => $guarantorData['personal']['selection_relationship_id'] ?? null,
                            'selection_nationality_id' => $guarantorData['personal']['selection_nationality_id'] ?? null,
                            'employment_name' => $guarantorData['employment']['employment_name'] ?? null,
                            'length_service_year' => $guarantorData['employment']['length_service_year'] ?? null,
                            'length_service_month' => $guarantorData['employment']['length_service_month'] ?? null,
                            'job_position' => $guarantorData['employment']['job_position'] ?? null,
                            'selection_terms_of_employment_id' => $guarantorData['employment']['selection_terms_of_employment_id'] ?? null,
                            'selection_occupation_id' => $guarantorData['employment']['selection_occupation_id'] ?? null,
                            'selection_business_classification_id' => $guarantorData['employment']['selection_business_classification_id'] ?? null,
                        ]);
                    } else {
                        // Create new guarantor
                        $loanGuarantor = $loan->loanGuarantors()->create([
                            'name' => $guarantorData['personal']['name'] ?? null,
                            'identity_no' => $guarantorData['personal']['identity_no'] ?? null,
                            'birth_date' => $guarantorData['personal']['birth_date'] ?? null,
                            'age' => $guarantorData['personal']['age'] ?? null,
                            'selection_gender_id' => $guarantorData['personal']['selection_gender_id'] ?? null,
                            'selection_relationship_id' => $guarantorData['personal']['selection_relationship_id'] ?? null,
                            'selection_nationality_id' => $guarantorData['personal']['selection_nationality_id'] ?? null,
                            'employment_name' => $guarantorData['employment']['employment_name'] ?? null,
                            'length_service_year' => $guarantorData['employment']['length_service_year'] ?? null,
                            'length_service_month' => $guarantorData['employment']['length_service_month'] ?? null,
                            'job_position' => $guarantorData['employment']['job_position'] ?? null,
                            'selection_terms_of_employment_id' => $guarantorData['employment']['selection_terms_of_employment_id'] ?? null,
                            'selection_occupation_id' => $guarantorData['employment']['selection_occupation_id'] ?? null,
                            'selection_business_classification_id' => $guarantorData['employment']['selection_business_classification_id'] ?? null,
                        ]);
                    }

                    // Update or create personal details (type = 0)
                    $personalDetail = $loanGuarantor->loanGuarantorDetails()->where('type', 0)->first();
                    if (! $personalDetail) {
                        $personalDetail = $loanGuarantor->loanGuarantorDetails()->create(['type' => 0]);
                    }

                    // Update or create personal contact
                    $personalContact = $guarantorData['personal']['contact'] ?? [];
                    if (isset($personalContact['telephone']) && ! empty($personalContact['telephone'])) {
                        if (isset($personalContact['telephone_id'])) {
                            $personalDetail->contact()->where('id', $personalContact['telephone_id'])->update([
                                'contact' => $personalContact['telephone'] ?? null,
                                'selection_country_id' => $personalContact['selection_telephone_country_id'] ?? null,
                                'selection_type_id' => 33,
                                'type' => null,
                            ]);
                        } else {
                            $personalDetail->contact()->create([
                                'contact' => $personalContact['telephone'] ?? null,
                                'selection_country_id' => $personalContact['selection_telephone_country_id'] ?? null,
                                'selection_type_id' => 33,
                                'category' => Contact::CATEGORY_TELEPHONE,
                                'type' => null,
                            ]);
                        }
                    }
                    if (isset($personalContact['mobile_phone']) && ! empty($personalContact['mobile_phone'])) {
                        if (isset($personalContact['mobile_id'])) {
                            $personalDetail->contact()->where('id', $personalContact['mobile_id'])->update([
                                'contact' => $personalContact['mobile_phone'] ?? null,
                                'selection_country_id' => $personalContact['selection_mobile_country_id'] ?? null,
                                'selection_type_id' => 32,
                                'type' => null,
                            ]);
                        } else {
                            $personalDetail->contact()->create([
                                'contact' => $personalContact['mobile_phone'] ?? null,
                                'selection_country_id' => $personalContact['selection_mobile_country_id'] ?? null,
                                'selection_type_id' => 32,
                                'category' => Contact::CATEGORY_MOBILE,
                                'type' => null,
                            ]);
                        }
                    }

                    // Update or create personal address
                    $personalAddress = $guarantorData['personal']['address'] ?? null;
                    if ($personalAddress && ! empty($personalAddress['line_1'])) {
                        if (isset($personalAddress['id'])) {
                            $personalDetail->address()->where('id', $personalAddress['id'])->update([
                                'line_1' => $personalAddress['line_1'] ?? null,
                                'line_2' => $personalAddress['line_2'] ?? null,
                                'postcode' => $personalAddress['postcode'] ?? null,
                                'city' => $personalAddress['city'] ?? null,
                                'selection_state_id' => $personalAddress['selection_state_id'] ?? null,
                                'selection_country_id' => $personalAddress['selection_country_id'] ?? null,
                            ]);
                        } else {
                            $personalDetail->address()->create([
                                'line_1' => $personalAddress['line_1'] ?? null,
                                'line_2' => $personalAddress['line_2'] ?? null,
                                'postcode' => $personalAddress['postcode'] ?? null,
                                'city' => $personalAddress['city'] ?? null,
                                'selection_state_id' => $personalAddress['selection_state_id'] ?? null,
                                'selection_country_id' => $personalAddress['selection_country_id'] ?? null,
                            ]);
                        }
                    }

                    // Update or create employment details (type = 1)
                    $employmentDetail = $loanGuarantor->loanGuarantorDetails()->where('type', 1)->first();
                    if (! $employmentDetail) {
                        $employmentDetail = $loanGuarantor->loanGuarantorDetails()->create(['type' => 1]);
                    }

                    // Update or create employment contact
                    $employmentContact = $guarantorData['employment']['contact'] ?? [];
                    if (isset($employmentContact['telephone']) && ! empty($employmentContact['telephone'])) {
                        if (isset($employmentContact['telephone_id'])) {
                            $employmentDetail->contact()->where('id', $employmentContact['telephone_id'])->update([
                                'contact' => $employmentContact['telephone'] ?? null,
                                'selection_country_id' => $employmentContact['selection_telephone_country_id'] ?? null,
                                'selection_type_id' => 33,
                                'type' => null,
                            ]);
                        } else {
                            $employmentDetail->contact()->create([
                                'contact' => $employmentContact['telephone'] ?? null,
                                'selection_country_id' => $employmentContact['selection_telephone_country_id'] ?? null,
                                'selection_type_id' => 33,
                                'category' => Contact::CATEGORY_TELEPHONE,
                                'type' => null,
                            ]);
                        }
                    }
                    if (isset($employmentContact['mobile_phone']) && ! empty($employmentContact['mobile_phone'])) {
                        if (isset($employmentContact['mobile_id'])) {
                            $employmentDetail->contact()->where('id', $employmentContact['mobile_id'])->update([
                                'contact' => $employmentContact['mobile_phone'] ?? null,
                                'selection_country_id' => $employmentContact['selection_mobile_country_id'] ?? null,
                                'selection_type_id' => 32,
                                'type' => null,
                            ]);
                        } else {
                            $employmentDetail->contact()->create([
                                'contact' => $employmentContact['mobile_phone'] ?? null,
                                'selection_country_id' => $employmentContact['selection_mobile_country_id'] ?? null,
                                'selection_type_id' => 32,
                                'category' => Contact::CATEGORY_MOBILE,
                                'type' => null,
                            ]);
                        }
                    }

                    // Update or create employment address
                    $employmentAddress = $guarantorData['employment']['address'] ?? null;
                    if ($employmentAddress && ! empty($employmentAddress['line_1'])) {
                        if (isset($employmentAddress['id'])) {
                            $employmentDetail->address()->where('id', $employmentAddress['id'])->update([
                                'line_1' => $employmentAddress['line_1'] ?? null,
                                'line_2' => $employmentAddress['line_2'] ?? null,
                                'postcode' => $employmentAddress['postcode'] ?? null,
                                'city' => $employmentAddress['city'] ?? null,
                                'selection_state_id' => $employmentAddress['selection_state_id'] ?? null,
                                'selection_country_id' => $employmentAddress['selection_country_id'] ?? null,
                            ]);
                        } else {
                            $employmentDetail->address()->create([
                                'line_1' => $employmentAddress['line_1'] ?? null,
                                'line_2' => $employmentAddress['line_2'] ?? null,
                                'postcode' => $employmentAddress['postcode'] ?? null,
                                'city' => $employmentAddress['city'] ?? null,
                                'selection_state_id' => $employmentAddress['selection_state_id'] ?? null,
                                'selection_country_id' => $employmentAddress['selection_country_id'] ?? null,
                            ]);
                        }
                    }
                }
            }

            if ($request->has('document')) {
                $document = $request->input('document', []);

                foreach ($document as $doc) {
                    if (! empty($doc['id']) && $doc['_delete']) {
                        $deleteDocument = LoanDocument::find($doc['id']);
                        if ($deleteDocument) {
                            // Delete related documents first
                            foreach ($deleteDocument->documents as $document) {
                                $document->delete();
                            }
                            // Delete the LoanDocument
                            $deleteDocument->delete();
                        }

                        continue;
                    }

                    if (isset($doc['doc_id'])) {
                        $document = CustomerDocument::find($doc['doc_id']);
                        $selectionDocumentType = Selection::find($document->selection_type_id);

                        $loanDocument = $loan->loanDocuments()->create([
                            'selection_type_id' => $selectionDocumentType?->id,
                            'type' => $selectionDocumentType?->value,
                        ]);

                        $doc = $document->documents()->first();

                        $loanDocument->documents()->create([
                            'category' => $doc->category,
                            'url' => $doc->url,
                        ]);
                    }
                }
            }
            $saveBankAction = false;
            if ($request->status >= 7 && ! empty($request->bank)) {
                $banks = $request->input('bank', []);

                foreach ($banks as $bank) {
                    if (isset($bank['save_bank']) && $bank['save_bank']) {
                        $saveBankAction = true;

                        if (isset($bank['_delete']) && $bank['_delete']) {
                            if (isset($bank['id'])) {
                                $loanBank = LoanBankAccount::findOrFail($bank['id']);
                                $loanBank->delete();
                            }

                            continue;
                        }

                        if (isset($bank['id'])) {
                            $loanBank = LoanBankAccount::findOrFail($bank['id']);
                            $loanBank->update([
                                'selection_bank_id' => $bank['selection_bank_id'] ?? null,
                                'selection_type_id' => $bank['selection_type_id'] ?? null,
                                'account_no' => $bank['account_no'] ?? null,
                                'account_name' => $bank['account_name'] ?? null,
                            ]);
                        } else {
                            $loan->loanBankAccounts()->create([
                                'selection_bank_id' => $bank['selection_bank_id'] ?? null,
                                'selection_type_id' => $bank['selection_type_id'] ?? null,
                                'account_no' => $bank['account_no'] ?? null,
                                'account_name' => $bank['account_name'] ?? null,
                            ]);
                        }
                    }
                }

                if ($saveBankAction) {
                    DB::commit();

                    return Redirect::route('loans.edit', $loan->fresh()->id)
                        ->with('success', 'Loan updated successfully.')
                        ->with('activeTab', $request->input('activeTab'));
                }
            }

            if ($request->status == 7 && ! $saveBankAction) {
                $loan->loanDetail->generateInstallments();
            }
            // dd($loan->loanDetail);
            DB::commit();

            return Redirect::route('loans.index')->with('success', 'Loan updated successfully.');
        } catch (\Exception $e) {
            DB::rollBack();
            dd($e->getMessage());

            return Redirect::back()->with('error', 'Failed to create loan: '.$e->getMessage())->withInput();
        }
    }

    /**
     * Remove the specified loan from storage.
     */
    public function destroy(Loan $loan): RedirectResponse
    {
        $this->authorize('delete', $loan);

        return Redirect::route('loans.index')->with('success', 'Loan deleted successfully.');
    }

    public function loanApi(Request $request)
    {
        $search = $request->search;
        $matches = [];
        $api = [
            [
                'value' => 'backlog',
                'label' => 'Backlog',
            ],
            [
                'value' => 'todo',
                'label' => 'Todo',
            ],
            [
                'value' => 'in progress',
                'label' => 'In Progress',
            ],
            [
                'value' => 'done',
                'label' => 'Done',
            ],
            [
                'value' => 'canceled',
                'label' => 'Canceled',
            ],
        ];

        foreach ($api as $item) {
            if (stripos($item['label'], $search) !== false) {
                $matches[] = $item;
            }
        }

        return $matches;
    }

    public function getCustomerDetail($id): JsonResponse
    {
        $customer = CustomerProfile::findOrFail($id);

        return response()->json($this->transformCustomerDetail($customer));
    }

    public function getCustomerCollaterals($customerCollateralId): JsonResponse
    {
        $customerCollateral = CustomerCollateral::with('collateral')->findOrFail($customerCollateralId);

        if (! $customerCollateral->collateral) {
            return response()->json(['error' => 'Collateral not found for the customer'], 404);
        }

        return response()->json(
            (new CollateralDetailResource($customerCollateral->collateral))->toArray(request(), true)
        );
    }

    public function getCustomerDocuments($customerId): JsonResponse
    {
        $customer = CustomerProfile::findOrFail($customerId);
        $customer_docs = [];
        foreach ($customer->customerDocuments as $customerDocument) {
            foreach ($customerDocument->documents as $document) {
                $customer_docs[] = [
                    'id' => $document->id,
                    'customer_doc_id' => $customerDocument->id,
                    'url' => $document->url,
                ];
            }
        }

        return response()->json($customer_docs);
    }

    private function transformCustomerDetail(CustomerProfile $customer): array
    {
        $teamName = $customer->teams->first()?->name;

        $contacts = [];

        if ($customer->customerContacts) {
            foreach ($customer->customerContacts as $customerContact) {
                foreach ($customerContact->contacts as $contact) {
                    $contacts[] = [
                        'id' => $contact->id,
                        'uuid' => $contact->uuid,
                        'selection_type_id' => $contact->selection_type_id,
                        'selection_country_id' => $contact->selection_country_id,
                        'contact_country_selection' => $contact->contactCountrySelection ? $contact->contactCountrySelection->value : null,
                        'contact' => $contact->contact,
                        'is_primary' => $contact->is_primary,
                        'can_receive_sms' => $contact->can_receive_sms,
                    ];
                }
            }
        }

        $addresses = [];

        if ($customer->customerAddresses) {
            foreach ($customer->customerAddresses as $customerAddress) {
                foreach ($customerAddress->addresses as $address) {
                    $addresses[] = [
                        'id' => $address->id,
                        'uuid' => $address->uuid,
                        'selection_type_id' => $address->selection_type_id,
                        'line_1' => $address->line_1,
                        'line_2' => $address->line_2,
                        'postcode' => $address->postcode,
                        'city' => $address->city,
                        'selection_state_id' => $address->selection_state_id,
                        'state_selection' => $address->stateSelection ? $address->stateSelection->value : null,
                        'selection_country_id' => $address->selection_country_id,
                        'country_selection' => $address->countrySelection ? $address->countrySelection->value : null,
                        'is_primary' => $address->is_primary,
                    ];
                }
            }
        }

        $companyOwners = [];

        if ($customer->company && $customer->company->owners) {
            foreach ($customer->company->owners as $companyowner) {
                $companyOwners[] = [
                    'id' => $companyowner->id,
                    'uuid' => $companyowner->uuid,
                    'name' => $companyowner->name,
                    'identity_no' => $companyowner->identity_no,
                    'selection_type_id' => $companyowner->selection_type_id,
                    'owner_type' => $companyowner->selectionTypes ? $companyowner->selectionTypes->value : null,
                    'selection_nationality_id' => $companyowner->selection_nationality_id,
                    'nationality_selection' => $companyowner->selectionNationality ? $companyowner->selectionNationality->value : null,
                    'share_unit' => $companyowner->share_unit,
                ];
            }
        }

        $collaterals = [];

        if ($customer->customerCollaterals) {
            foreach ($customer->customerCollaterals as $customerCollateral) {
                $collateral = $customerCollateral->collateral;

                $property = $collateral->property ?? null;

                // Extract valuers
                $valuers = [];
                if ($collateral) {
                    foreach ($collateral->valuers as $valuer) {
                        $valuers[] = [
                            'id' => $valuer->id,
                            'valuer' => $valuer->valuer,
                            'valuation_amount' => $valuer->valuation_amount,
                            'valuation_received_date' => $valuer->valuation_received_date,
                            'land_search_received_date' => $valuer->land_search_received_date,
                            'is_primary' => $valuer->is_primary,
                        ];
                    }

                    $propertyOwner = [];
                    if ($property && $property->propertyOwners) {
                        foreach ($property->propertyOwners as $owner) {
                            $ownerContacts = [];

                            foreach ($owner->contacts as $ownerContact) {
                                $ownerContacts[] = [
                                    'id' => $ownerContact->id ?? null,
                                    'uuid' => $ownerContact->uuid ?? null,
                                    'selection_type_id' => $ownerContact->selection_type_id ?? null,
                                    'selection_country_id' => $ownerContact->selection_country_id ?? null,
                                    'contact' => $ownerContact->contact ?? null,
                                    'is_primary' => $ownerContact->is_primary ?? null,
                                ];
                            }

                            $propertyOwner[] = [
                                'id' => $owner->id,
                                'name' => $owner->name,
                                'identity_no' => $owner->identity_no,
                                'selection_telephone_country_id' => $owner->telephone()?->selection_country_id,
                                'selection_mobile_country_id' => $owner->mobilePhone()?->selection_country_id,
                                'telephone_country_selection' => $owner->telephone()?->contactCountrySelection ? $owner->telephone()->contactCountrySelection->value : null,
                                'mobile_country_selection' => $owner->mobilePhone()?->contactCountrySelection ? $owner->mobilePhone()->contactCountrySelection->value : null,
                                'telephone' => $owner->telephone,
                                'mobile_phone' => $owner->mobilePhone,
                                'address' => [
                                    'id' => $owner->address->id ?? null,
                                    'line_1' => $owner->address->line_1 ?? null,
                                    'line_2' => $owner->address->line_2 ?? null,
                                    'postcode' => $owner->address->postcode ?? null,
                                    'city' => $owner->address->city ?? null,
                                    'selection_state_id' => $owner->address->selection_state_id ?? null,
                                    'selection_country_id' => $owner->address->selection_country_id ?? null,
                                ],
                            ];
                        }
                    }

                    $collaterals[] = [
                        'id' => $customerCollateral->id,
                        'collateral_id' => $customerCollateral->collateral_id,
                        'customer_id' => $customerCollateral->customer_id,
                        'selection_customer_type_id' => $collateral->selection_customer_type_id,
                        'selection_type_id' => $collateral->selection_type_id,
                        'typeSelection' => $collateral->typeSelection ? $collateral->typeSelection->value : null,
                        'name' => $collateral->name,
                        'identity_no' => $collateral->identity_no,
                        'remark' => $collateral->remark,
                        'property' => $property ? [
                            'id' => $property->id,
                            'ownership_no' => $property->ownership_no,
                            'lot_number' => $property->lot_number,
                            'selection_land_category_id' => $property->selection_land_category_id,
                            'land_category' => $property->land_category,
                            'land_category_selection' => $property->landCategorySelection ? $property->landCategorySelection->value : null,
                            'land_category_other' => $property->land_category_other,
                            'type_of_property' => $property->type_of_property,
                            'selection_type_of_property_id' => $property->selection_type_of_property_id,
                            'type_of_property_selection' => $property->propertyTypesSelection ? $property->propertyTypesSelection->value : null,
                            'selection_land_size_unit' => $property->selection_land_size_unit,
                            'land_size_unit_selection' => $property->landSizeSelection ? $property->landSizeSelection->value : null,
                            'land_size' => $property->land_size,
                            'selection_land_status_id' => $property->selection_land_status_id,
                            'land_status' => $property->land_status,
                            'land_status_selection' => $property->landStatusSelection ? $property->landStatusSelection->value : null,
                            'no_syit_piawai' => $property->no_syit_piawai,
                            'certified_plan_no' => $property->certified_plan_no,
                            'selection_built_up_area_unit' => $property->selection_built_up_area_unit,
                            'built_up_area_of_property' => $property->built_up_area_of_property,
                            'built_up_area_unit_selection' => $property->builtUpAreaSelection ? $property->builtUpAreaSelection->value : null,
                            'city' => $property->city,
                            'location' => $property->location,
                            'district' => $property->district,
                            'address' => [
                                'line_1' => $property->address->line_1 ?? null,
                                'line_2' => $property->address->line_2 ?? null,
                                'postcode' => $property->address->postcode ?? null,
                                'city' => $property->address->city ?? null,
                                'selection_state_id' => $property->address->selection_state_id ?? null,
                                'state_selection' => $property->address->stateSelection ? $property->address->stateSelection->value : null,
                                'selection_country_id' => $property->address->selection_country_id ?? null,
                                'country_selection' => $property->address->countrySelection ? $property->address->countrySelection->value : null,
                            ],
                        ] : null,
                        'valuers' => $valuers,
                        'owners' => $propertyOwner,
                    ];

                }
            }
        }

        $documents = [];

        if ($customer->customerDocuments) {
            foreach ($customer->customerDocuments as $customerDocument) {
                foreach ($customerDocument->documents as $document) {

                    $path = $document->url;
                    $fileContent = $this->getUploadedFile($path);

                    $documents[] = [
                        'id' => $customerDocument->id,
                        'customer_id' => $customerDocument->customer_id,
                        'document_id' => $document->id,
                        'url' => $path,
                        'category' => $document->category,
                        'selection_type_id' => $customerDocument->selection_type_id,
                        'file' => [
                            'name' => $fileContent['name'] ?? null,
                            'size' => $fileContent['size'] ?? null,
                            'url' => $fileContent['url'] ?? null,
                        ],
                        'created_at' => $document->created_at,
                        'uploaded_by' => $document->createdBy ? [
                            'id' => $document->createdBy->id,
                            'username' => $document->createdBy->username,
                        ] : null,
                    ];
                }
            }
        }

        return [
            'id' => $customer->id,
            'uuid' => $customer->uuid,
            'team' => $teamName,
            'selection_type_id' => $customer->selection_type_id,
            'customer_type' => $customer->selectionType ? $customer->selectionType->value : null,
            'name' => $customer->name,
            'identity_no' => $customer->identity_no,
            'old_identity_no' => $customer->old_identity_no ? $customer->old_identity_no : null,
            'registration_date' => $customer->registration_date ? $customer->registration_date : null,
            'years_of_incorporation' => $customer->years_of_incorporation ? $customer->years_of_incorporation : null,
            'age' => $customer->age ? $customer->age : null,
            'birth_date' => $customer->birth_date ? $customer->birth_date : null,
            'selection_gender_id' => $customer->selection_gender_id ? $customer->selection_gender_id : null,
            'gender_selection' => $customer->selectionGender ? $customer->selectionGender->value : null,
            'selection_race_id' => $customer->selection_race_id ? $customer->selection_race_id : null,
            'race_selection' => $customer->selectionRace ? $customer->selectionRace->value : null,
            'selection_nationality_id' => $customer->selection_nationality_id ? $customer->selection_nationality_id : null,
            'nationality_selection' => $customer->selectionNationality ? $customer->selectionNationality->value : null,
            'selection_education_level_id' => $customer->selection_education_level_id ? $customer->selection_education_level_id : null,
            'education_level_selection' => $customer->selectionEducationLevel ? $customer->selectionEducationLevel->value : null,
            'selection_marriage_status_id' => $customer->selection_marriage_status_id ? $customer->selection_marriage_status_id : null,
            'marriage_status_selection' => $customer->selectionMarriageStatus ? $customer->selectionMarriageStatus->value : null,
            'remark' => $customer->remark ? $customer->remark : null,
            'contacts' => $contacts,
            'addresses' => $addresses,
            'employment' => $customer->employment ? [
                'id' => $customer->employment->id,
                'uuid' => $customer->employment->uuid,
                'employer_name' => $customer->employment->employer_name,
                'length_service_year' => $customer->employment->length_service_year,
                'length_service_month' => $customer->employment->length_service_month,
                'job_position' => $customer->employment->job_position,
                'selection_terms_of_employment_id' => $customer->employment->selection_terms_of_employment_id,
                'terms_of_employment_selection' => $customer->employment->selectionTermsOfEmployment ? $customer->employment->selectionTermsOfEmployment->value : null,
                'selection_occupation_id' => $customer->employment->selection_occupation_id,
                'occupation_selection' => $customer->employment->selectionOccupation ? $customer->employment->selectionOccupation->value : null,
                'selection_business_category_id' => $customer->employment->selection_business_category_id,
                'business_category_selection' => $customer->employment->selectionBusinessCategory ? $customer->employment->selectionBusinessCategory->value : null,
                'gross_income' => $customer->employment->gross_income,
                'net_income' => $customer->employment->net_income,
                'selection_telephone_country_id' => $customer->employment->telephone()?->selection_country_id,
                'selection_mobile_country_id' => $customer->employment->mobilePhone()?->selection_country_id,
                'telephone_country_selection' => $customer->employment->telephone()?->contactCountrySelection ? $customer->employment->telephone()->contactCountrySelection->value : null,
                'mobile_country_selection' => $customer->employment->mobilePhone()?->contactCountrySelection ? $customer->employment->mobilePhone()->contactCountrySelection->value : null,
                'telephone' => $customer->employment->telephone,
                'mobile_phone' => $customer->employment->mobilePhone,
                'address' => $customer->employment->address ? [
                    'id' => $customer->employment->address->id,
                    'line_1' => $customer->employment->address->line_1,
                    'line_2' => $customer->employment->address->line_2,
                    'postcode' => $customer->employment->address->postcode,
                    'city' => $customer->employment->address->city,
                    'selection_state_id' => $customer->employment->address->selection_state_id,
                    'state_selection' => $customer->employment->address->stateSelection ? $customer->employment->address->stateSelection->value : null,
                    'selection_country_id' => $customer->employment->address->selection_country_id,
                    'country_selection' => $customer->employment->address->countrySelection ? $customer->employment->address->countrySelection->value : null,
                ] : null,
            ] : null,
            'company' => $customer->company ? [
                'id' => $customer->company->id,
                'uuid' => $customer->company->uuid,
                'current_paid_up_capital' => $customer->company->current_paid_up_capital,
                'business_turnover' => $customer->company->business_turnover,
                'business_turnover_date' => $customer->company->business_turnover_date,
                'business_net_income' => $customer->company->business_net_income,
                'business_net_income_date' => $customer->company->business_net_income_date,
                'selection_nature_of_business_id' => $customer->company->selection_nature_of_business_id,
                'nature_of_business_selection' => $customer->company->selectionNatureOfBusiness ? $customer->company->selectionNatureOfBusiness->value : null,
                'selection_country_of_business_id' => $customer->company->selection_country_of_business_id,
                'country_of_business_selection' => $customer->company->selectionCountryOfBusiness ? $customer->company->selectionCountryOfBusiness->value : null,
            ] : null,
            'owners' => $companyOwners,
            'collaterals' => $collaterals,
            'documents' => $documents,
        ];
    }

    public function transactionTab(Request $request, Loan $loan): Response
    {
        $from = request()->query('from', 'default');

        $loan->load(['loanDetail']);

        // $this->authorize('transaction', $loan);

        // Installments
        $loanInstallments = LoanInstallment::withLoanId($loan->id)->orderBy('tenure', 'asc');
        $currentInstallment = LoanInstallment::withLoanId($loan->id)->withCurrentInstallment()->first();
        $balanceInstallment = LoanInstallment::getBalanceInstallment($loan->id, $currentInstallment->tenure, $loan->loanDetail->rebate);

        // Transactions
        $loanTransactions = LoanTxn::withLoanId($loan->id)->orderedByTypeAndDate()->byStatus(LoanTxnStatus::UNPAID);
        $balanceTransaction = LoanTxn::getBalanceTransaction($loan->id, $currentInstallment->tenure);

        // Payments
        $paymentRecords = $this->applyPagination($loanInstallments, $request, 5,
            fn ($payment) => (new LoanPaymentResource($payment))->toArray($request));

        return Inertia::render('loans/Transaction', [
            'loan' => (new LoanDetailResource($loan))->toArray(request(), true),
            'installments' => $this->applyPagination($loanInstallments, $request, 10,
                fn ($installment) => (new LoanInstallmentResource($installment))->toArray($request)),
            'balanceInstallment' => $balanceInstallment,
            'transactions' => $this->applyPagination($loanTransactions, $request, 10,
                fn ($transaction) => (new LoanTransactionResource($transaction))->toArray($request)),
            'balanceTransaction' => $balanceTransaction,
            'paymentRecords' => $paymentRecords,
            'current' => [
                'tenure' => $currentInstallment->tenure,
                'due_date' => $currentInstallment->due_date,
            ],
            'from' => $from,
            'transactionTypes' => $this->getSelectionsOptionsForCategory('transaction_type'),
            'letterTypes' => $this->getSelectionsOptionsForCategory('letter_type'),
        ]);
    }

    public function transactionPost(Request $request, Loan $loan): RedirectResponse
    {
        // $this->authorize('transaction', $loan);

        $validated = $request->validate([
            'selection_transaction_type_id' => 'required|exists:selections,id',
            'amount' => 'required|numeric|min:0',
            'payment_date' => 'required|string|max:255',
            'remark' => 'nullable|string|max:500',
        ]);

        DB::beginTransaction();

        try {
            if ($validated['selection_transaction_type_id'] == 391) {
                $loan->generatePostageTransaction($loan->id, 1, 1, $request->input('amount'), $request->input('remark'));
            } else if ($validated['selection_transaction_type_id'] == 392) {
                $loan->generateMiscChargeTransaction($loan->id, 1, 1, $request->input('amount'), $request->input('remark'));
            } else if ($validated['selection_transaction_type_id'] == 393) {
                $loan->generateLegalFeeTransaction($loan->id, 1, 1, $request->input('amount'), $request->input('remark'));
            }

            DB::commit();

            return Redirect::route('loan.transaction', ['loan' => $loan, 'from' => 'edit'])
                ->with('success', 'Transaction added successfully.');
        } catch (\Exception $e) {
            DB::rollBack();
            dd($e->getMessage());

            return Redirect::back()->with('error', 'Failed to add transaction: '.$e->getMessage())->withInput();
        }
    }
}
