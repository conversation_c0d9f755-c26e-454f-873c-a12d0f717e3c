<script setup lang="ts">
import Collateral from '@/components/customer/Collateral.vue';
import FaIcon from '@/components/FaIcon.vue';
import FormField from '@/components/form/FormField.vue';
import FormShow from '@/components/form/FormShow.vue';
import Heading from '@/components/Heading.vue';
import TabsWrapper from '@/components/TabsWrapper.vue';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardFooter } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import { useFormatOptions } from '@/composables/useFormatOptions';
import { useFormSubmit } from '@/composables/useFormSubmit';
import AppLayout from '@/layouts/AppLayout.vue';
import { Head, useForm } from '@inertiajs/vue3';
import axios from 'axios';
import { computed, onMounted, reactive, ref, watch } from 'vue';

const { formatSelectionOptions } = useFormatOptions();
const { submitWithConfirmation } = useFormSubmit();

const keywords = ['name', 'identity_no'];
const icons = ['circle-user', 'id-card'];
const defaultSearchValue = 'identity_no';

const collateralKeywords = ['remark', 'ownership_no', 'lot_number'];
const collateralIcons = ['circle-user', 'id-card'];
const defaultCollateralSearchValue = 'remark';

const handleResults = (customerId: number) => {
    fetchBorrowerDetail(customerId);
};

const handleCoBorrowerResults = (customerId: number) => {
    fetchCoBorrowerDetail(customerId);
};

const handleCollateralResults = (collateral: number) => {
    fetchCustomerCollaterals(collateral);
};

interface Company {
    id: number;
    display_name: string;
    code: string;
    headquarter_id: number | null;
}

interface Team {
    id: number;
    name: string;
    company_id: number | null;
    company_name: string | null;
}

interface Agent {
    id: number;
    name: string;
    code: string;
    company_id: number | null;
}

interface Props {
    companies: Company[];
    teams: Team[];
    headquarters: Company[];
    agents: Agent[];
    loanTypes: Array<{
        id: number;
        value: string;
    }>;
    loanModes: Array<{
        id: number;
        value: string;
    }>;
    repaymentMethods: Array<{
        id: number;
        value: string;
    }>;
    genderTypes: Array<{
        id: number;
        value: string;
    }>;
    relationships: Array<{
        id: number;
        value: string;
    }>;
    nationalities: Array<{
        id: number;
        value: string;
    }>;
    telephoneCountries: Array<{
        id: number;
        value: string;
    }>;
    mobileCountries: Array<{
        id: number;
        value: string;
    }>;
    states: Array<{
        id: number;
        value: string;
    }>;
    countries: Array<{
        id: number;
        value: string;
    }>;
    employmentTerms: Array<{
        id: number;
        value: string;
    }>;
    businessClassifications: Array<{
        id: number;
        value: string;
    }>;
    occupations: Array<{
        id: number;
        value: string;
    }>;
    collateralTypes: Array<{
        id: number;
        value: string;
    }>;
    documentTypes: Array<{
        id: number;
        value: string;
    }>;
}

type Contact = {
    selection_telephone_country_id: number | null;
    selection_mobile_country_id: number | null;
    telephone: string | null;
    mobile_phone: string | null;
};

type Address = {
    line_1: string | null;
    line_2: string | null;
    postcode: string | null;
    city: string | null;
    selection_state_id: number | null;
    selection_country_id: number | null;
};

const props = defineProps<Props>();
const ordinal = (n: number) => {
    const s = ['th', 'st', 'nd', 'rd'];
    const v = n % 100;
    return n + (s[(v - 20) % 10] || s[v] || s[0]);
};
const openAccordionIndex = ref<string | undefined>(undefined);
const selectedDocIds = ref<(number | undefined)[]>([]);
const activeTab = ref('personal');
const activeDocTab = ref('all');
const COLLATERAL_TYPE_ID = props.loanTypes.find((type) => type.value === 'Collateral')?.id ?? null;

const form = useForm({
    headquarter_id: null as number | null,
    company_id: null as number | null,
    team_id: null as number | null,
    agent_id: null as number | null,
    selection_type_id: null as number | null,
    borrower_id: null as number | null,
    search_input: null as string | null,
    search_value: null as string | null,
    _saveAsDraft: null as boolean | null,

    co_borrower: {
        ids: [] as number[],
    },

    collateral: {
        ids: [],
    },

    loan: {
        selection_mode_type_id: null as number | null,
        loan_principle_amount: null as string | null,
        no_of_instalment: null as string | null,
        last_payment: null as string | null,
        instalment_amount: null as number | null,
        selection_repayment_method_id: null as number | null,
        late_payment_charges: 8.0,
        interest: null as string | null,
        selection_interest_period: null as number | null,
    },

    emergency: {
        personal: {
            name: null as string | null,
            identity_no: null as string | null,
            birth_date: null as string | null,
            age: null as number | null,
            selection_gender_id: null as number | null,
            selection_relationship_id: null as number | null,
            selection_nationality_id: null as number | null,
            contact: {} as Contact,
            address: {} as Address,
        },
        employment: {
            employment_name: null as string | null,
            contact: {} as Contact,
            address: {} as Address,
        },
    },

    guarantor: [] as any,

    document: [] as any,
});

watch(
    () => ({
        loanAmount: form.loan.loan_principle_amount,
        interestRate: form.loan.interest,
        totalInstallments: form.loan.no_of_instalment,
        lastPayment: form.loan.last_payment,
    }),
    (values) => {
        const allFieldsFilled = Object.values(values).every((val) => val !== null && val !== '');

        if (!allFieldsFilled) {
            form.loan.instalment_amount = null;
            return;
        }

        const P = Number(values.loanAmount);
        const R = Number(values.interestRate) / 100;
        const N = Number(values.totalInstallments);
        const LP = Number(values.lastPayment);

        const totalInterest = P * R * N;
        const totalPayable = P + totalInterest;

        const remainingInstallments = N - 1;

        if (remainingInstallments <= 0) {
            form.loan.instalment_amount = Number((totalPayable - LP).toFixed(2));
            return;
        }

        const emi = (totalPayable - LP) / remainingInstallments;

        form.loan.instalment_amount = isFinite(emi) ? Number(emi.toFixed(2)) : null;
    },
);

const addGuarantor = () => {
    form.guarantor.push({
        personal: {
            name: null,
            identity_no: null,
            birth_date: null,
            age: null,
            selection_gender_id: null,
            selection_relationship_id: null,
            selection_nationality_id: null,
            contact: {
                selection_telephone_country_id: null,
                selection_mobile_country_id: null,
                telephone: null,
                mobile_phone: null,
            },
            address: {
                line_1: null,
                line_2: null,
                postcode: null,
                city: null,
                selection_state_id: null,
                selection_country_id: null,
            },
        },
        employment: {
            selection_terms_of_employment_id: null,
            length_service_year: null,
            employment_name: null,
            length_service_month: null,
            job_position: null,
            selection_business_classification_id: null,
            selection_occupation_id: null,
            contact: {
                selection_telephone_country_id: null,
                selection_mobile_country_id: null,
                telephone: null,
                mobile_phone: null,
            },
            address: {
                line_1: null,
                line_2: null,
                postcode: null,
                city: null,
                selection_state_id: null,
                selection_country_id: null,
            },
        },
        _delete: false,
    });
    openAccordionIndex.value = (form.guarantor.length - 1).toString();
};

const removeGuarantor = (index: number) => {
    form.guarantor.splice(index, 1);
};

const personalFields = computed(() => [
    {
        id: 'headquarter_id',
        label: 'Headquarter Name',
        type: 'select',
        required: true,
        placeholder: 'Headquarter Name',
        error: form.errors.headquarter_id,
        options: formatSelectionOptions(props.headquarters, 'id', 'display_name'),
        modelValue: form.headquarter_id,
        updateValue: (value: number) => (form.headquarter_id = value),
    },
    {
        id: 'company_id',
        label: 'Company Name',
        type: 'select',
        required: true,
        placeholder: 'Company Name',
        error: form.errors.company_id,
        options: formatSelectionOptions(filteredCompanies.value, 'id', 'display_name'),
        modelValue: form.company_id,
        updateValue: (value: number) => (form.company_id = value),
    },
    {
        id: 'team_id',
        label: 'Team Name',
        type: 'select',
        required: true,
        placeholder: 'Team Name',
        error: form.errors.team_id,
        options: formatSelectionOptions(filteredTeams.value, 'id', 'name'),
        modelValue: form.team_id,
        updateValue: (value: number) => (form.team_id = value),
    },
    {
        id: 'agent_id',
        label: 'Agent',
        type: 'select',
        required: true,
        placeholder: 'Agent',
        error: form.errors.agent_id,
        options: formatSelectionOptions(filteredAgents.value, 'id', 'name'),
        modelValue: form.agent_id,
        updateValue: (value: number) => (form.agent_id = value),
    },
    {
        id: 'borrower_id',
        label: 'Main Borrower',
        type: 'input_search',
        required: true,
        placeholder: 'Main Borrower Name/Identity No',
        error: form.errors.borrower_id,
        modelValue: form.borrower_id,
        updateValue: (value: number) => (form.borrower_id = value),
        api: '/api/customers/search',
        icons: icons,
        labelKeywords: keywords,
        defaultSearchValue: defaultSearchValue,
        onResultsUpdated: handleResults,
    },
    {
        id: 'loan_type',
        label: 'Loan Type',
        type: 'select',
        required: true,
        placeholder: 'Loan Type',
        error: form.errors.selection_type_id,
        options: formatSelectionOptions(props.loanTypes),
        modelValue: form.selection_type_id,
        updateValue: (value: number) => (form.selection_type_id = value),
    },
]);

const loanFields = computed(() => [
    {
        id: 'loan_mode',
        label: 'Loan Mode',
        type: 'select',
        required: true,
        placeholder: 'Loan Mode',
        error: form.errors['loan.selection_mode_type_id'],
        options: formatSelectionOptions(props.loanModes),
        modelValue: form.loan.selection_mode_type_id,
        updateValue: (value: number) => (form.loan.selection_mode_type_id = value),
    },
    {
        id: 'loan_principle_of_amount',
        label: 'Loan Principle of Amount',
        type: 'currency',
        required: true,
        placeholder: 'Loan Principle of Amount',
        error: form.errors['loan.loan_principle_amount'],
        modelValue: form.loan.loan_principle_amount,
        updateValue: (value: string) => (form.loan.loan_principle_amount = value),
    },
    {
        id: 'no_of_instalment',
        label: 'No of Instalment',
        type: 'input',
        required: true,
        placeholder: 'No of Instalment',
        error: form.errors['loan.no_of_instalment'],
        modelValue: form.loan.no_of_instalment,
        updateValue: (value: string) => (form.loan.no_of_instalment = value),
    },
    {
        id: 'last_payment',
        label: 'Last Payment',
        type: 'currency',
        required: true,
        placeholder: 'Last Payment',
        error: form.errors['loan.last_payment'],
        modelValue: form.loan.last_payment,
        updateValue: (value: string) => (form.loan.last_payment = value),
    },
    {
        id: 'repayment_method',
        label: 'Repayment Method',
        type: 'select',
        required: true,
        placeholder: 'Repayment Method',
        error: form.errors['loan.selection_repayment_method_id'],
        options: formatSelectionOptions(props.repaymentMethods),
        modelValue: form.loan.selection_repayment_method_id,
        updateValue: (value: number) => (form.loan.selection_repayment_method_id = value),
    },
    {
        id: 'loan_instalment_amount',
        label: 'Instalment Amount (RM)',
        type: 'show',
        required: true,
        placeholder: 'Instalment Amount (RM)',
        error: form.errors['loan.instalment_amount'],
        modelValue: form.loan.instalment_amount,
        updateValue: (value: string) => (form.loan.instalment_amount = value),
    },
    {
        id: 'interest',
        label: 'Interest (%)',
        type: 'inputSelect',
        required: true,
        placeholder: 'Interest',
        error: form.errors['loan.interest'],
        selectPosition: 'right',
        modelValue: {
            select: form.loan.selection_mode_type_id,
            input: form.loan.interest,
        },
        updateValue: (value: { select: number; input: string }) => {
            form.loan.interest = value.input;
            form.loan.selection_interest_period = value.select;
        },
        options: formatSelectionOptions(props.loanModes),
    },
    {
        id: 'late-payment-charges',
        label: 'Late Payment Charges (%)',
        type: 'show',
        required: true,
        placeholder: 'Late Payment Charges (%)',
        modelValue: form.loan.late_payment_charges,
        updateValue: (value: string) => (form.loan.late_payment_charges = parseFloat(value)),
    },
]);

const emergencyFields = computed(() => [
    {
        id: 'emergency_name',
        label: 'Name',
        type: 'input',
        required: false,
        placeholder: 'Name',
        error: form.errors['emergency.personal.name'],
        modelValue: form.emergency.personal.name,
        updateValue: (value: string) => (form.emergency.personal.name = value),
    },
    {
        id: 'emergency_identity_no',
        label: 'Identity No',
        type: 'input',
        required: false,
        placeholder: 'Identity No',
        error: form.errors['emergency.personal.identity_no'],
        modelValue: form.emergency.personal.identity_no,
        updateValue: (value: string) => (form.emergency.personal.identity_no = value),
    },
    {
        id: 'emergency_birth_date',
        label: 'Date of Birth',
        type: 'date',
        required: false,
        placeholder: 'Date of Birth',
        error: form.errors['emergency.personal.birth_date'],
        modelValue: form.emergency.personal.birth_date,
        updateValue: (value: string) => (form.emergency.personal.birth_date = value),
    },
    {
        id: 'emergency_age',
        label: 'Age',
        type: 'show',
        required: false,
        placeholder: 'Age',
        error: form.errors['emergency.personal.age'],
        modelValue: form.emergency.personal.age,
        updateValue: (value: number) => (form.emergency.personal.age = value),
    },
    {
        id: 'emergency_gender',
        label: 'Gender',
        type: 'radio',
        required: false,
        placeholder: 'Gender',
        options: formatSelectionOptions(props.genderTypes),
        error: form.errors['emergency.personal.selection_gender_id'],
        modelValue: form.emergency.personal.selection_gender_id,
        updateValue: (value: number) => (form.emergency.personal.selection_gender_id = value),
    },
    {
        id: 'relationship',
        label: 'Relationship',
        type: 'select',
        required: false,
        placeholder: 'Relationship',
        error: form.errors['emergency.personal.selection_relationship_id'],
        options: formatSelectionOptions(props.relationships),
        modelValue: form.emergency.personal.selection_relationship_id,
        updateValue: (value: number) => (form.emergency.personal.selection_relationship_id = value),
    },
    {
        id: 'nationality',
        label: 'Nationality',
        type: 'select',
        class: 'col-span-1',
        required: false,
        placeholder: 'Nationality',
        error: form.errors['emergency.personal.selection_nationality_id'],
        options: formatSelectionOptions(props.nationalities),
        modelValue: form.emergency.personal.selection_nationality_id,
        updateValue: (value: number) => (form.emergency.personal.selection_nationality_id = value),
    },
    {},
    {
        id: 'telephone',
        label: 'Telephone No',
        type: 'inputSelect',
        required: false,
        placeholder: 'Telephone No',
        selectPlaceholder: 'Select',
        error: form.errors['emergency.personal.contact.telephone'],
        modelValue: {
            select: form.emergency.personal.contact.selection_telephone_country_id,
            input: form.emergency.personal.contact.telephone,
        },
        updateValue: (value: { select: number; input: string }) => {
            form.emergency.personal.contact.telephone = value.input;
            form.emergency.personal.contact.selection_telephone_country_id = value.select;
        },
        options: formatSelectionOptions(props.telephoneCountries),
    },
    {
        id: 'mobile',
        label: 'Mobile Phone',
        type: 'inputSelect',
        required: false,
        placeholder: 'Mobile Phone',
        selectPlaceholder: 'Select',
        error: form.errors['emergency.personal.contact.mobile_phone'],
        modelValue: {
            select: form.emergency.personal.contact.selection_mobile_country_id,
            input: form.emergency.personal.contact.mobile_phone,
        },
        updateValue: (value: { select: number; input: string }) => {
            form.emergency.personal.contact.mobile_phone = value.input;
            form.emergency.personal.contact.selection_mobile_country_id = value.select;
        },
        options: formatSelectionOptions(props.mobileCountries),
    },
    {
        id: 'emergency_line_1',
        label: 'Address Line 1',
        type: 'input',
        class: 'col-span-2',
        required: false,
        placeholder: 'Address Line 1',
        error: form.errors['emergency.personal.address.line_1'],
        modelValue: form.emergency.personal.address.line_1,
        updateValue: (value: string) => (form.emergency.personal.address.line_1 = value),
    },
    {
        id: 'line_2',
        label: 'Address Line 2',
        type: 'input',
        class: 'col-span-2',
        required: false,
        placeholder: 'Address Line 2',
        error: form.errors['emergency.personal.address.line_2'],
        modelValue: form.emergency.personal.address.line_2,
        updateValue: (value: string) => (form.emergency.personal.address.line_2 = value),
    },
    {
        id: 'postcode',
        label: 'Postcode',
        type: 'input',
        required: false,
        placeholder: 'Postcode',
        error: form.errors['emergency.personal.address.postcode'],
        modelValue: form.emergency.personal.address.postcode,
        updateValue: (value: string) => (form.emergency.personal.address.postcode = value),
    },
    {
        id: 'city',
        label: 'City',
        type: 'input',
        required: false,
        placeholder: 'City',
        error: form.errors['emergency.personal.address.city'],
        modelValue: form.emergency.personal.address.city,
        updateValue: (value: string) => (form.emergency.personal.address.city = value),
    },
    {
        id: 'state',
        label: 'State',
        type: 'select',
        required: false,
        placeholder: 'State',
        error: form.errors['emergency.personal.address.selection_state_id'],
        options: formatSelectionOptions(props.states),
        modelValue: form.emergency.personal.address.selection_state_id,
        updateValue: (value: number) => (form.emergency.personal.address.selection_state_id = value),
    },
    {
        id: 'country',
        label: 'Country',
        type: 'select',
        required: false,
        placeholder: 'Country',
        error: form.errors['emergency.personal.address.selection_country_id'],
        options: formatSelectionOptions(props.countries),
        modelValue: form.emergency.personal.address.selection_country_id,
        updateValue: (value: number) => (form.emergency.personal.address.selection_country_id = value),
    },
]);

const emergencyEmploymentFields = computed(() => [
    {
        id: 'emergency_employment_name',
        label: 'Name',
        type: 'input',
        required: false,
        class: 'col-span-1',
        placeholder: 'Name',
        error: form.errors['emergency.employment.employment_name'],
        modelValue: form.emergency.employment.employment_name,
        updateValue: (value: string) => (form.emergency.employment.employment_name = value),
    },
    {},
    {
        id: 'emergency_employment_telephone',
        label: 'Telephone No',
        type: 'inputSelect',
        required: false,
        placeholder: 'Telephone No',
        selectPlaceholder: 'Select',
        error: form.errors['emergency.employment.contact.telephone'],
        modelValue: {
            select: form.emergency.employment.contact.selection_telephone_country_id,
            input: form.emergency.employment.contact.telephone,
        },
        updateValue: (value: { select: number; input: string }) => {
            form.emergency.employment.contact.telephone = value.input;
            form.emergency.employment.contact.selection_telephone_country_id = value.select;
        },
        options: formatSelectionOptions(props.telephoneCountries),
    },
    {
        id: 'emergency_employment_mobile',
        label: 'Mobile Phone',
        type: 'inputSelect',
        required: false,
        placeholder: 'Mobile Phone',
        selectPlaceholder: 'Select',
        error: form.errors['emergency.employment.contact.mobile_phone'],
        modelValue: {
            select: form.emergency.employment.contact.selection_mobile_country_id,
            input: form.emergency.employment.contact.mobile_phone,
        },
        updateValue: (value: { select: number; input: string }) => {
            form.emergency.employment.contact.mobile_phone = value.input;
            form.emergency.employment.contact.selection_mobile_country_id = value.select;
        },
        options: formatSelectionOptions(props.mobileCountries),
    },
    {
        id: 'emergency_employment_line_1',
        label: 'Address Line 1',
        type: 'input',
        class: 'col-span-2',
        required: false,
        placeholder: 'Address Line 1',
        error: form.errors['emergency.employment.address.line_1'],
        modelValue: form.emergency.employment.address.line_1,
        updateValue: (value: string) => (form.emergency.employment.address.line_1 = value),
    },
    {
        id: 'emergency_employment_line_2',
        label: 'Address Line 2',
        type: 'input',
        class: 'col-span-2',
        required: false,
        placeholder: 'Address Line 2',
        error: form.errors['emergency.employment.address.line_2'],
        modelValue: form.emergency.employment.address.line_2,
        updateValue: (value: string) => (form.emergency.employment.address.line_2 = value),
    },
    {
        id: 'emergency_employment_postcode',
        label: 'Postcode',
        type: 'input',
        required: false,
        placeholder: 'Postcode',
        error: form.errors['emergency.employment.address.postcode'],
        modelValue: form.emergency.employment.address.postcode,
        updateValue: (value: string) => (form.emergency.employment.address.postcode = value),
    },
    {
        id: 'emergency_employment_city',
        label: 'City',
        type: 'input',
        required: false,
        placeholder: 'City',
        error: form.errors['emergency.employment.address.city'],
        modelValue: form.emergency.employment.address.city,
        updateValue: (value: string) => (form.emergency.employment.address.city = value),
    },
    {
        id: 'emergency_employment_state',
        label: 'State',
        type: 'select',
        required: false,
        placeholder: 'State',
        error: form.errors['emergency.employment.address.selection_state_id'],
        options: formatSelectionOptions(props.states),
        modelValue: form.emergency.employment.address.selection_state_id,
        updateValue: (value: number) => (form.emergency.employment.address.selection_state_id = value),
    },
    {
        id: 'emergency_employment_country',
        label: 'Country',
        type: 'select',
        required: false,
        placeholder: 'Country',
        error: form.errors['emergency.employment.address.selection_country_id'],
        options: formatSelectionOptions(props.countries),
        modelValue: form.emergency.employment.address.selection_country_id,
        updateValue: (value: number) => (form.emergency.employment.address.selection_country_id = value),
    },
]);

const getGuarantorFields = (index: number) => [
    {
        id: `guarantor_name_${index}`,
        label: 'Name',
        type: 'input',
        required: false,
        placeholder: 'Name',
        error: (form.errors as any)?.[`guarantor.${index}.personal.name`],
        modelValue: form.guarantor[index].personal.name,
        updateValue: (value: string) => (form.guarantor[index].personal.name = value),
    },
    {
        id: `guarantor_identity_no_${index}`,
        label: 'Identity No',
        type: 'input',
        required: false,
        placeholder: 'Identity No',
        error: (form.errors as any)?.[`guarantor.${index}.personal.identity_no`],
        modelValue: form.guarantor[index].personal.identity_no,
        updateValue: (value: string) => (form.guarantor[index].personal.identity_no = value),
    },
    {
        id: `guarantor_birth_date_${index}`,
        label: 'Date of Birth',
        type: 'date',
        required: false,
        placeholder: 'Date of Birth',
        error: (form.errors as any)?.[`guarantor.${index}.personal.birth_date`],
        modelValue: form.guarantor[index].personal.birth_date,
        updateValue: (value: string) => (form.guarantor[index].personal.birth_date = value),
    },
    {
        id: `guarantor_age_${index}`,
        label: 'Age',
        type: 'show',
        required: false,
        placeholder: 'Age',
        error: (form.errors as any)?.[`guarantor.${index}.personal.age`],
        modelValue: form.guarantor[index].personal.age,
        updateValue: (value: number) => (form.guarantor[index].personal.age = value),
    },
    {
        id: `guarantor_gender_${index}`,
        label: 'Gender',
        type: 'radio',
        required: false,
        placeholder: 'Gender',
        options: formatSelectionOptions(props.genderTypes),
        error: (form.errors as any)?.[`guarantor.${index}.personal.selection_gender_id`],
        modelValue: form.guarantor[index].personal.selection_gender_id,
        updateValue: (value: number) => (form.guarantor[index].personal.selection_gender_id = value),
    },
    {
        id: `guarantor_relationship_${index}`,
        label: 'Relationship',
        type: 'select',
        required: false,
        placeholder: 'Relationship',
        options: formatSelectionOptions(props.relationships),
        error: (form.errors as any)?.[`guarantor.${index}.personal.selection_relationship_id`],
        modelValue: form.guarantor[index].personal.selection_relationship_id,
        updateValue: (value: number) => (form.guarantor[index].personal.selection_relationship_id = value),
    },
    {
        id: `guarantor_nationality_${index}`,
        label: 'Nationality',
        type: 'select',
        required: false,
        placeholder: 'Nationality',
        options: formatSelectionOptions(props.nationalities),
        error: (form.errors as any)?.[`guarantor.${index}.personal.selection_nationality_id`],
        modelValue: form.guarantor[index].personal.selection_nationality_id,
        updateValue: (value: number) => (form.guarantor[index].personal.selection_nationality_id = value),
    },
    {},
    {
        id: `guarantor_telephone_${index}`,
        label: 'Telephone No',
        type: 'inputSelect',
        required: false,
        placeholder: 'Telephone No',
        selectPlaceholder: 'Select',
        options: formatSelectionOptions(props.telephoneCountries),
        error: (form.errors as any)?.[`guarantor.${index}.personal.contact.telephone`],
        modelValue: {
            select: form.guarantor[index].personal.contact.selection_telephone_country_id,
            input: form.guarantor[index].personal.contact.telephone,
        },
        updateValue: (value: { select: number; input: string }) => {
            form.guarantor[index].personal.contact.telephone = value.input;
            form.guarantor[index].personal.contact.selection_telephone_country_id = value.select;
        },
    },
    {
        id: `guarantor_mobile_${index}`,
        label: 'Mobile Phone',
        type: 'inputSelect',
        required: false,
        placeholder: 'Mobile Phone',
        selectPlaceholder: 'Select',
        options: formatSelectionOptions(props.mobileCountries),
        error: (form.errors as any)?.[`guarantor.${index}.personal.contact.mobile_phone`],
        modelValue: {
            select: form.guarantor[index].personal.contact.selection_mobile_country_id,
            input: form.guarantor[index].personal.contact.mobile_phone,
        },
        updateValue: (value: { select: number; input: string }) => {
            form.guarantor[index].personal.contact.mobile_phone = value.input;
            form.guarantor[index].personal.contact.selection_mobile_country_id = value.select;
        },
    },
    {
        id: `guarantor_line_1_${index}`,
        label: 'Address Line 1',
        type: 'input',
        required: false,
        class: 'col-span-2',
        placeholder: 'Address Line 1',
        error: (form.errors as any)?.[`guarantor.${index}.personal.address.line_1`],
        modelValue: form.guarantor[index].personal.address.line_1,
        updateValue: (value: string) => (form.guarantor[index].personal.address.line_1 = value),
    },
    {
        id: `guarantor_line_2_${index}`,
        label: 'Address Line 2',
        type: 'input',
        required: false,
        class: 'col-span-2',
        placeholder: 'Address Line 2',
        error: (form.errors as any)?.[`guarantor.${index}.personal.address.line_2`],
        modelValue: form.guarantor[index].personal.address.line_2,
        updateValue: (value: string) => (form.guarantor[index].personal.address.line_2 = value),
    },
    {
        id: `guarantor_postcode_${index}`,
        label: 'Postcode',
        type: 'input',
        required: false,
        placeholder: 'Postcode',
        error: (form.errors as any)?.[`guarantor.${index}.personal.address.postcode`],
        modelValue: form.guarantor[index].personal.address.postcode,
        updateValue: (value: string) => (form.guarantor[index].personal.address.postcode = value),
    },
    {
        id: `guarantor_city_${index}`,
        label: 'City',
        type: 'input',
        required: false,
        placeholder: 'City',
        error: (form.errors as any)?.[`guarantor.${index}.personal.address.city`],
        modelValue: form.guarantor[index].personal.address.city,
        updateValue: (value: string) => (form.guarantor[index].personal.address.city = value),
    },
    {
        id: `guarantor_state_${index}`,
        label: 'State',
        type: 'select',
        required: false,
        placeholder: 'State',
        options: formatSelectionOptions(props.states),
        error: (form.errors as any)?.[`guarantor.${index}.personal.address.selection_state_id`],
        modelValue: form.guarantor[index].personal.address.selection_state_id,
        updateValue: (value: number) => (form.guarantor[index].personal.address.selection_state_id = value),
    },
    {
        id: `guarantor_country_${index}`,
        label: 'Country',
        type: 'select',
        required: false,
        placeholder: 'Country',
        options: formatSelectionOptions(props.countries),
        error: (form.errors as any)?.[`guarantor.${index}.personal.address.selection_country_id`],
        modelValue: form.guarantor[index].personal.address.selection_country_id,
        updateValue: (value: number) => (form.guarantor[index].personal.address.selection_country_id = value),
    },
];

const getGuarantorEmploymentFields = (index: number) => [
    {
        id: `guarantor_terms_of_employment_${index}`,
        label: 'Terms of Employment',
        type: 'select',
        required: false,
        placeholder: 'Terms of Employment',
        options: formatSelectionOptions(props.employmentTerms),
        error: (form.errors as any)?.[`guarantor.${index}.employment.selection_terms_of_employment_id`],
        modelValue: form.guarantor[index].employment.selection_terms_of_employment_id,
        updateValue: (value: number) => (form.guarantor[index].employment.selection_terms_of_employment_id = value),
    },
    {
        id: `guarantor_length_service_year_${index}`,
        label: 'Length Service Year',
        type: 'input',
        required: false,
        placeholder: 'Length Service Year',
        error: (form.errors as any)?.[`guarantor.${index}.employment.length_service_year`],
        modelValue: form.guarantor[index].employment.length_service_year,
        updateValue: (value: string) => (form.guarantor[index].employment.length_service_year = value),
    },
    {
        id: `guarantor_employment_name_${index}`,
        label: 'Employment Name',
        type: 'input',
        required: false,
        placeholder: 'Employment Name',
        error: (form.errors as any)?.[`guarantor.${index}.employment.employment_name`],
        modelValue: form.guarantor[index].employment.employment_name,
        updateValue: (value: string) => (form.guarantor[index].employment.employment_name = value),
    },
    {
        id: `guarantor_length_service_month_${index}`,
        label: 'Length Service Month',
        type: 'input',
        required: false,
        placeholder: 'Length Service Month',
        error: (form.errors as any)?.['guarantor.0.employment.length_service_month'],
        modelValue: form.guarantor[index].employment.length_service_month,
        updateValue: (value: string) => (form.guarantor[index].employment.length_service_month = value),
    },
    {
        id: `guarantor_job_position_${index}`,
        label: 'Job Position',
        type: 'input',
        required: false,
        placeholder: 'Job Position',
        error: (form.errors as any)?.[`guarantor.${index}.employment.job_position`],
        modelValue: form.guarantor[index].employment.job_position,
        updateValue: (value: string) => (form.guarantor[index].employment.job_position = value),
    },
    {
        id: `guarantor_business_classification_${index}`,
        label: 'Business Classification',
        type: 'select',
        required: false,
        placeholder: 'Business Classification',
        options: formatSelectionOptions(props.businessClassifications),
        error: (form.errors as any)?.[`guarantor.${index}.employment.selection_business_classification_id`],
        modelValue: form.guarantor[index].employment.selection_business_classification_id,
        updateValue: (value: number) => (form.guarantor[index].employment.selection_business_classification_id = value),
    },
    {
        id: `guarantor_occupation_${index}`,
        label: 'Occupation',
        type: 'select',
        required: false,
        placeholder: 'Occupation',
        options: formatSelectionOptions(props.occupations),
        error: (form.errors as any)?.[`guarantor.${index}.employment.selection_occupation_id`],
        modelValue: form.guarantor[index].employment.selection_occupation_id,
        updateValue: (value: number) => (form.guarantor[index].employment.selection_occupation_id = value),
    },
    {},
    {
        id: `employment_telephone_${index}`,
        label: 'Telephone No',
        type: 'inputSelect',
        required: false,
        placeholder: 'Telephone',
        selectPlaceholder: 'Select',
        options: formatSelectionOptions(props.telephoneCountries),
        error: (form.errors as any)?.[`guarantor.${index}.employment.contact.telephone`],
        modelValue: {
            select: form.guarantor[index].employment.contact.selection_telephone_country_id,
            input: form.guarantor[index].employment.contact.telephone,
        },
        updateValue: (value: { select: number; input: string }) => {
            form.guarantor[index].employment.contact.telephone = value.input;
            form.guarantor[index].employment.contact.selection_telephone_country_id = value.select;
        },
    },
    {
        id: `employment_mobile_${index}`,
        label: 'Mobile Phone',
        type: 'inputSelect',
        required: false,
        placeholder: 'Mobile Phone',
        selectPlaceholder: 'Select',
        options: formatSelectionOptions(props.mobileCountries),
        error: (form.errors as any)?.[`guarantor.${index}.employment.contact.mobile_phone`],
        modelValue: {
            select: form.guarantor[index].employment.contact.selection_mobile_country_id,
            input: form.guarantor[index].employment.contact.mobile_phone,
        },
        updateValue: (value: { select: number; input: string }) => {
            form.guarantor[index].employment.contact.mobile_phone = value.input;
            form.guarantor[index].employment.contact.selection_mobile_country_id = value.select;
        },
    },
    {
        id: `employment_line_1_${index}`,
        label: 'Address Line 1',
        type: 'input',
        class: 'col-span-2',
        required: false,
        placeholder: 'Address Line 1',
        error: (form.errors as any)?.[`guarantor.${index}.employment.address.line_1`],
        modelValue: form.guarantor[index].employment.address.line_1,
        updateValue: (value: string) => (form.guarantor[index].employment.address.line_1 = value),
    },
    {
        id: `employment_line_2_${index}`,
        label: 'Address Line 2',
        type: 'input',
        class: 'col-span-2',
        required: false,
        placeholder: 'Address Line 2',
        error: (form.errors as any)?.[`guarantor.${index}.employment.address.line_2`],
        modelValue: form.guarantor[index].employment.address.line_2,
        updateValue: (value: string) => (form.guarantor[index].employment.address.line_2 = value),
    },
    {
        id: `employment_postcode_${index}`,
        label: 'Postcode',
        type: 'input',
        required: false,
        placeholder: 'Postcode',
        error: (form.errors as any)?.[`guarantor.${index}.employment.address.postcode`],
        modelValue: form.guarantor[index].employment.address.postcode,
        updateValue: (value: string) => (form.guarantor[index].employment.address.postcode = value),
    },
    {
        id: `employment_city_${index}`,
        label: 'City',
        type: 'input',
        required: false,
        placeholder: 'City',
        error: (form.errors as any)?.[`guarantor.${index}.employment.address.city`],
        modelValue: form.guarantor[index].employment.address.city,
        updateValue: (value: string) => (form.guarantor[index].employment.address.city = value),
    },
    {
        id: `employment_state_${index}`,
        label: 'State',
        type: 'select',
        required: false,
        placeholder: 'State',
        options: formatSelectionOptions(props.states),
        error: (form.errors as any)?.[`guarantor.${index}.employment.address.selection_state_id`],
        modelValue: form.guarantor[index].employment.address.selection_state_id,
        updateValue: (value: number) => (form.guarantor[index].employment.address.selection_state_id = value),
    },
    {
        id: `employment_country_${index}`,
        label: 'Country',
        type: 'select',
        required: false,
        placeholder: 'Country',
        options: formatSelectionOptions(props.countries),
        error: (form.errors as any)?.[`guarantor.${index}.employment.address.selection_country_id`],
        modelValue: form.guarantor[index].employment.address.selection_country_id,
        updateValue: (value: number) => (form.guarantor[index].employment.address.selection_country_id = value),
    },
];

const filteredCompanies = computed(() => {
    if (!form.headquarter_id) {
        return [];
    }
    return props.companies.filter((company) => company.headquarter_id === form.headquarter_id);
});

// Filter teams based on selected company
const filteredTeams = computed(() => {
    if (!form.company_id) return [];
    return props.teams.filter((team) => team?.company_id === form.company_id);
});

const filteredAgents = computed(() => {
    if (!form.team_id || !form.company_id) return [];
    return props.agents.filter((agent) => agent.companies?.some((company: any) => company.id === form.company_id));
});

const tabFlow = computed(() => ({
    personal: form.selection_type_id === COLLATERAL_TYPE_ID ? 'collateral' : 'loan',
    collateral: 'loan',
    loan: 'emergency',
    emergency: 'guarantor',
    guarantor: 'document',
    document: null,
}));

const tabBackFlow = computed(() => ({
    collateral: 'personal',
    loan: form.selection_type_id === COLLATERAL_TYPE_ID ? 'collateral' : 'personal',
    emergency: 'loan',
    guarantor: 'emergency',
    document: 'guarantor',
}));

const goToNextTab = () => {
    const current = activeTab.value as keyof typeof tabFlow.value;
    const next = tabFlow.value[current];
    if (next) activeTab.value = next;
};

const goToPreviousTab = () => {
    const current = activeTab.value as keyof typeof tabBackFlow.value;
    const prev = tabBackFlow.value[current];
    if (prev) activeTab.value = prev;
};

const tabItems = computed(() => [
    { label: 'Personal Details', value: 'personal' },
    { label: 'Collateral Details', value: 'collateral', disabled: form.selection_type_id !== COLLATERAL_TYPE_ID },
    { label: 'Loan Details', value: 'loan' },
    { label: 'Emergency Contact', value: 'emergency' },
    { label: 'Guarantor Contact', value: 'guarantor' },
    { label: 'Supporting Document', value: 'document' },
]);

watch(
    () => form.selection_type_id,
    (newVal, oldVal) => {
        const isNowCollateral = newVal === COLLATERAL_TYPE_ID;
        const wasCollateral = oldVal === COLLATERAL_TYPE_ID;

        if (!isNowCollateral && wasCollateral) {
            // User switched *away* from "Collateral" → clean up
            form.collateral.ids = [];
        }
    },
);

const docItems = computed(() => [
    { label: 'All', value: 'all' },
    { label: 'Customer Documents', value: 'customer-doc' },
    { label: 'Collateral Documents', value: 'collateral-doc' },
    { label: 'Security Documents', value: 'security-doc' },
]);

type FileData = {
    id?: number;
    name: string;
    size: number;
    url: string;
    typeId: number;
    type: string;
    createdAt: string;
    uploadedBy: string;
};

type TabKey = 'customer-doc' | 'collateral-doc' | 'security-doc' | 'all';
const categorizedFiles = reactive<Record<TabKey, FileData[]>>({
    'customer-doc': [],
    'collateral-doc': [],
    'security-doc': [],
    all: [],
});

const CUSTOMER_DOC_ID = props.documentTypes.find((type) => type.value === 'Customer Documents')?.id ?? null;
const COLLATERAL_DOC_ID = props.documentTypes.find((type) => type.value === 'Collateral Documents')?.id ?? null;
const SECURITY_DOC_ID = props.documentTypes.find((type) => type.value === 'Security Documents')?.id ?? null;

const allDocs = computed(() => {
    return Object.values(categorizedFiles).flat(); // includes all docs from all tabs
});

const selectedFile = computed(() => {
    const id = selectedDocIds.value[0];
    return allDocs.value.find((doc) => doc.id === id) || null;
});

const toggleSelect = (id: number | undefined) => {
    if (id === undefined) return;

    const currentIndex = selectedDocIds.value.indexOf(id);

    if (currentIndex > -1) {
        // Already selected → remove and mark as deleted
        selectedDocIds.value.splice(currentIndex, 1);
        form.document = form.document.filter((item) => item.id !== id);
    } else {
        // Not selected → add with _delete: false
        selectedDocIds.value.push(id);
        const exists = form.document.some((item) => item.id === id);
        if (!exists) {
            form.document = [...form.document, { id, _delete: false }];
        } else {
            form.document = form.document.map((item) => (item.id === id ? { id, _delete: false } : item));
        }
    }
};

const typeToTabKey: Record<number, TabKey> = {};

if (CUSTOMER_DOC_ID) typeToTabKey[CUSTOMER_DOC_ID] = 'customer-doc';
if (COLLATERAL_DOC_ID) typeToTabKey[COLLATERAL_DOC_ID] = 'collateral-doc';
if (SECURITY_DOC_ID) typeToTabKey[SECURITY_DOC_ID] = 'security-doc';

const isImage = (fileName: string): boolean => {
    const ext = fileName.split('.').pop()?.toLowerCase();
    return ['jpg', 'jpeg', 'png'].includes(ext ?? '');
};

const submit = (isDraft = false) => {
    if (isDraft) {
        form._saveAsDraft = true;
    } else {
        form._saveAsDraft = false;
    }

    submitWithConfirmation({
        form,
        submitOptions: {
            method: 'post',
            url: route('loans.store'),
            entityName: 'loan',
        },
    });
};

onMounted(() => {
    if (form.guarantor.length > 0) {
        openAccordionIndex.value = '0';
    }
});

const borrowersDocuments = ref<any[]>([]);
const borrower = ref(null);
const coBorrowers = ref([]);
const fetchBorrowerDetail = async (customerId: number) => {
    try {
        const response = await axios.get(`/api/loans/customer/${customerId}/detail`);
        borrower.value = response.data;
        form.borrower_id = customerId;

        if (response.data.documents && Array.isArray(response.data.documents)) {
            borrowersDocuments.value = [...response.data.documents]; // Initialize with borrower docs
            categorizeDocuments(borrowersDocuments.value);
        }
    } catch (error) {
        console.error('Error fetching customer detail', error);
    }
};

const removeCoBorrowers = (index: number) => {
    const removedCoBorrower = coBorrowers.value[index];

    // 1. Remove co-borrower from coBorrowers list
    coBorrowers.value.splice(index, 1);

    // 2. Remove co-borrower ID from form
    form.co_borrower.ids.splice(index, 1);
    // 3. Remove co-borrower's collaterals from collateralsPool
    if (removedCoBorrower) {
        removedCoBorrower.collaterals.forEach((collateral: any) => {
            // Remove from form.collateral.ids
            const formIndex = form.collateral.ids.indexOf(collateral.id);
            if (formIndex !== -1) {
                form.collateral.ids.splice(formIndex, 1);
            }
        });
    }

    if (removedCoBorrower?.documents) {
        borrowersDocuments.value = borrowersDocuments.value.filter((doc) => doc.customer_id !== removedCoBorrower.id);

        const removedDocIds = removedCoBorrower.documents.map((doc: any) => doc.id);

        selectedDocIds.value = selectedDocIds.value.filter((id) => !removedDocIds.includes(id));
        form.document.ids = form.document.ids.filter((id) => !removedDocIds.includes(id));

        categorizeDocuments(borrowersDocuments.value); // Re-categorize after cleanup
    }
};

const removeCollateral = (index: number) => {
    const removed = selectedCollaterals.value.splice(index, 1)[0];

    if (removed && removed.customer_collateral_id) {
        const idIndex = form.collateral.ids.indexOf(removed.customer_collateral_id);
        if (idIndex !== -1) {
            form.collateral.ids.splice(idIndex, 1);
        }
    }
};

const fetchCoBorrowerDetail = async (customerId: number) => {
    try {
        const response = await axios.get(`/api/loans/customer/${customerId}/detail`);

        coBorrowers.value.push(response.data);

        if (!form.co_borrower.ids.includes(customerId)) {
            form.co_borrower.ids.push(customerId);
        }

        if (response.data.documents && Array.isArray(response.data.documents)) {
            borrowersDocuments.value.push(...response.data.documents);
            categorizeDocuments(borrowersDocuments.value);
        }
    } catch (error) {
        console.error('Error fetching co-borrower detail', error);
    }
};

const fetchCustomerCollaterals = async (collateral?: number) => {
    if (!collateral) return;

    try {
        const response = await axios.get(`/api/loans/collateral/${collateral}`);
        const collateralData = response.data;
        collateralData.customer_collateral_id = collateral;

        collateralsPool.value.push(collateralData);
        if (!form.collateral.ids.includes(collateral)) {
            form.collateral.ids.push(collateral);
        }
    } catch (error) {
        console.error('Error fetching collateral detail:', error);
    }
};

const collateralsPool = ref<any[]>([]);

const selectedCollaterals = computed(() => form.collateral.ids.map((id) => collateralsPool.value.find((c) => c.customer_collateral_id === id)));

const categorizeDocuments = (docs: any[]) => {
    // Clear current docs
    categorizedFiles['customer-doc'] = [];
    categorizedFiles['collateral-doc'] = [];
    categorizedFiles['security-doc'] = [];
    categorizedFiles['all'] = [];

    docs.forEach((doc) => {
        const fileData: FileData = {
            id: doc.id,
            name: doc.file?.name ?? 'Unnamed',
            size: doc.file?.size ?? 0,
            typeId: doc.selection_type_id,
            url: `${window.location.origin}/storage/` + doc.url,
            type: doc.file?.name?.split('.').pop() ?? 'file',
            createdAt: doc.created_at,
            uploadedBy: doc.uploaded_by?.username ?? '-',
        };

        categorizedFiles['all'].push(fileData);
        if (doc.selection_type_id === CUSTOMER_DOC_ID) {
            categorizedFiles['customer-doc'].push(fileData);
        } else if (doc.selection_type_id === COLLATERAL_DOC_ID) {
            categorizedFiles['collateral-doc'].push(fileData);
        } else if (doc.selection_type_id === SECURITY_DOC_ID) {
            categorizedFiles['security-doc'].push(fileData);
        }
    });
};

const calculateAge = (birthDateStr: string | null): number | null => {
    if (!birthDateStr) return null;
    const birthDate = new Date(birthDateStr);
    const today = new Date();

    let age = today.getFullYear() - birthDate.getFullYear();
    const m = today.getMonth() - birthDate.getMonth();

    if (m < 0 || (m === 0 && today.getDate() < birthDate.getDate())) {
        age--;
    }

    return age;
};

watch(
    () => form.emergency.personal.birth_date,
    (newVal) => {
        form.emergency.personal.age = calculateAge(newVal);
    },
);

watch(
    () => form.guarantor.map((g: any) => g.personal.birth_date),
    (newDates) => {
        newDates.forEach((date: any, index: number) => {
            form.guarantor[index].personal.age = calculateAge(date);
        });
    },
    { deep: true },
);
</script>

<template>
    <AppLayout>
        <Head title="Create Loan" />
        <div class="px-4 py-3">
            <Heading title="Loans" pageNumber="P000011" description="Create a new loan record" />
            <form @submit.prevent="submit">
                <Card class="gap-0 py-0">
                    <CardHeader class="bg-azure gap-0 rounded-t-lg px-5.5 py-3 text-white">
                        <CardTitle>Add New Loan</CardTitle>
                    </CardHeader>
                    <TabsWrapper v-model="activeTab" :tabs="tabItems">
                        <template #personal>
                            <CardContent class="py-4">
                                <Label class="text-[20px]" for="">Personal Info</Label>
                                <div class="mt-1 grid gap-4 lg:grid-cols-2">
                                    <FormField
                                        v-for="field in personalFields"
                                        :key="field.id"
                                        :id="field.id"
                                        :label="field.label"
                                        :model-value="field.modelValue"
                                        @update:model-value="field.updateValue"
                                        :type="field.type"
                                        :required="field.required"
                                        :placeholder="field.placeholder"
                                        :error="field.error"
                                        :options="field.options"
                                        :api="field.api"
                                        :labelKeywords="field.labelKeywords"
                                        :defaultSearchValue="field.defaultSearchValue"
                                        :icons="field.icons"
                                        @resultsUpdated="field.onResultsUpdated ? field.onResultsUpdated($event) : () => {}"
                                    />
                                </div>
                                <FormShow v-if="borrower" :customer="borrower" :ordinal="ordinal" />
                                <div class="mt-4">
                                    <Label class="text-[20px]" for="">Co-Borrower Info</Label>
                                    <p class="text-steel mb-2 text-sm italic">Search & Add Co-Borrower</p>
                                    <div class="grid gap-4 lg:grid-cols-2">
                                        <FormField
                                            id="co-borrower-search"
                                            label="Co-Borrower"
                                            placeholder="Co Borrower Name/Identity No"
                                            type="input_search"
                                            :api="'/api/customers/search'"
                                            @resultsUpdated="handleCoBorrowerResults"
                                            :labelKeywords="keywords"
                                            :defaultSearchValue="defaultSearchValue"
                                            :icons="icons"
                                        />
                                    </div>
                                    <FormShow
                                        v-for="(customer, index) in coBorrowers"
                                        :index="index"
                                        :label="`Co-Borrower ${index + 1}`"
                                        :customer="customer"
                                        :removeCoBorrowers="removeCoBorrowers"
                                        :ordinal="ordinal"
                                    />
                                </div>
                            </CardContent>
                            <CardFooter class="bg-stone mb-0 flex items-center justify-end gap-2 rounded-b-xl px-4 py-3">
                                <Button variant="outline" @click="goToNextTab" type="button" class="bg-cobalt flex items-center gap-2 text-white">
                                    Next
                                    <FaIcon name="chevron-right" />
                                </Button>
                            </CardFooter>
                        </template>

                        <template #collateral>
                            <CardContent class="py-4">
                                <Label class="text-[20px]">Collateral Info</Label>
                                <p class="text-steel mb-2 text-sm italic">Search & Add New Collateral Details</p>
                                <div class="grid gap-4 lg:grid-cols-2">
                                    <FormField
                                        id="collateral-search"
                                        label="Collateral"
                                        placeholder="Search"
                                        type="input_search"
                                        :api="'/api/collaterals/search'"
                                        @resultsUpdated="handleCollateralResults"
                                        :labelKeywords="['remark', 'ownership_no', 'lot_number']"
                                        :icons="['gavel', 'house-user', 'clipboard-list']"
                                        :defaultSearchValue="'remark'"
                                        :params="{ customer_ids: [form.borrower_id, ...form.co_borrower.ids] }"
                                    />
                                </div>
                                <Collateral
                                    :form="selectedCollaterals"
                                    :collateralTypes="props.collateralTypes"
                                    :ordinal="ordinal"
                                    isAccordion
                                    isShow
                                    isApplyLoan
                                    :removeCollateral="removeCollateral"
                                />
                            </CardContent>
                            <CardFooter class="bg-stone mb-0 flex items-center justify-end gap-2 rounded-b-xl px-4 py-3">
                                <Button
                                    variant="outline"
                                    @click="goToPreviousTab"
                                    type="button"
                                    class="bg-card text-muted-foreground flex items-center gap-2"
                                >
                                    <FaIcon name="chevron-left" />
                                    Back
                                </Button>
                                <Button variant="outline" @click="goToNextTab" type="button" class="bg-cobalt flex items-center gap-2 text-white">
                                    Next
                                    <FaIcon name="chevron-right" />
                                </Button>
                            </CardFooter>
                        </template>
                        <template #loan>
                            <CardContent class="py-4">
                                <Label class="text-[20px]">Loan Details</Label>
                                <div class="grid gap-4 lg:grid-cols-2">
                                    <FormField
                                        v-for="field in loanFields"
                                        :key="field.id"
                                        :id="field.id"
                                        :label="field.label"
                                        :model-value="field.modelValue"
                                        @update:model-value="field.updateValue"
                                        :type="field.type"
                                        :required="field.required"
                                        :placeholder="field.placeholder"
                                        :error="field.error"
                                        :options="field.options"
                                        :class="field.class"
                                        :selectPosition="field.selectPosition"
                                    />
                                </div>
                            </CardContent>
                            <CardFooter class="bg-stone mb-0 flex items-center justify-end gap-2 rounded-b-xl px-4 py-3">
                                <Button
                                    variant="outline"
                                    @click="goToPreviousTab"
                                    type="button"
                                    class="bg-card text-muted-foreground flex items-center gap-2"
                                >
                                    <FaIcon name="chevron-left" />
                                    Back
                                </Button>
                                <Button variant="outline" @click="goToNextTab" type="button" class="bg-cobalt flex items-center gap-2 text-white">
                                    Next
                                    <FaIcon name="chevron-right" />
                                </Button>
                            </CardFooter>
                        </template>
                        <template #emergency>
                            <CardContent class="py-4">
                                <Label class="text-[20px]">Emergency Contact</Label>
                                <div class="grid gap-4 lg:grid-cols-2">
                                    <FormField
                                        v-for="field in emergencyFields"
                                        :key="field.id"
                                        :id="field.id"
                                        :label="field.label"
                                        :model-value="field.modelValue"
                                        @update:model-value="field.updateValue"
                                        :selectPlaceholder="field.selectPlaceholder"
                                        :type="field.type"
                                        :required="field.required"
                                        :placeholder="field.placeholder"
                                        :error="field.error"
                                        :options="field.options"
                                        :class="field.class"
                                        :selectPosition="field.selectPosition"
                                    />
                                </div>
                                <Label class="pt-4 text-[20px]">Emergency Contact's Employment info</Label>
                                <div class="grid gap-4 lg:grid-cols-2">
                                    <FormField
                                        v-for="field in emergencyEmploymentFields"
                                        :key="field.id"
                                        :id="field.id"
                                        :label="field.label"
                                        :model-value="field.modelValue"
                                        @update:model-value="field.updateValue"
                                        :type="field.type"
                                        :required="field.required"
                                        :placeholder="field.placeholder"
                                        :error="field.error"
                                        :options="field.options"
                                        :class="field.class"
                                        :selectPosition="field.selectPosition"
                                    />
                                </div>
                            </CardContent>
                            <CardFooter class="bg-stone mb-0 flex items-center justify-end gap-2 rounded-b-xl px-4 py-3">
                                <Button
                                    variant="outline"
                                    @click="goToPreviousTab"
                                    type="button"
                                    class="bg-card text-muted-foreground flex items-center gap-2"
                                >
                                    <FaIcon name="chevron-left" />
                                    Back
                                </Button>
                                <Button variant="outline" @click="goToNextTab" type="button" class="bg-cobalt flex items-center gap-2 text-white">
                                    Next
                                    <FaIcon name="chevron-right" />
                                </Button>
                            </CardFooter>
                        </template>
                        <template #guarantor>
                            <CardContent class="py-4">
                                <div class="flex justify-between pb-3">
                                    <Label class="text-[20px]">Guarantor Contact</Label>
                                    <Button type="button" class="bg-teal hover:bg-teal-hover flex items-center gap-2" @click="addGuarantor">
                                        <FaIcon name="plus" />
                                        Add New
                                    </Button>
                                </div>
                                <Accordion type="single" class="w-full" collapsible v-model="openAccordionIndex">
                                    <AccordionItem v-for="(guarantor, index) in form.guarantor" :key="index" :value="String(index)" class="mb-1">
                                        <Card v-if="!guarantor._delete" class="gap-0 rounded-xs py-0">
                                            <AccordionTrigger class="group flex w-full items-center justify-between gap-2 p-2 hover:no-underline">
                                                <span class="bg-cloud p-1.5 group-data-[state=open]:hidden">
                                                    <FaIcon name="plus" />
                                                </span>

                                                <!-- Minus icon: visible when open -->
                                                <span class="bg-cloud hidden p-1.5 group-data-[state=open]:block">
                                                    <FaIcon name="minus" />
                                                </span>

                                                <span class="flex-1 text-left font-medium"> {{ ordinal(index + 1) }} Guarantor </span>

                                                <template #icon>
                                                    <Button
                                                        type="button"
                                                        @click.stop="removeGuarantor(index)"
                                                        variant="destructive"
                                                        class="flex items-center gap-1"
                                                    >
                                                        <FaIcon name="trash" />
                                                        Delete
                                                    </Button>
                                                </template>
                                            </AccordionTrigger>
                                            <Separator />
                                            <AccordionContent class="p-2">
                                                <div class="grid gap-4 lg:grid-cols-2">
                                                    <FormField
                                                        v-for="field in getGuarantorFields(index)"
                                                        :key="field.id"
                                                        :id="field.id"
                                                        :label="field.label"
                                                        :model-value="field.modelValue"
                                                        @update:model-value="field.updateValue"
                                                        :type="field.type"
                                                        :required="field.required"
                                                        :selectPlaceholder="field.selectPlaceholder"
                                                        :placeholder="field.placeholder"
                                                        :error="field.error"
                                                        :options="field.options"
                                                        :class="field.class"
                                                        :selectPosition="field.selectPosition"
                                                    />
                                                </div>
                                                <Label class="pt-4 text-[20px]">Guarantor Contact's Employment info</Label>
                                                <div class="grid gap-4 lg:grid-cols-2">
                                                    <FormField
                                                        v-for="field in getGuarantorEmploymentFields(index)"
                                                        :key="field.id"
                                                        :id="field.id"
                                                        :label="field.label"
                                                        :model-value="field.modelValue"
                                                        @update:model-value="field.updateValue"
                                                        :type="field.type"
                                                        :required="field.required"
                                                        :placeholder="field.placeholder"
                                                        :error="field.error"
                                                        :options="field.options"
                                                        :class="field.class"
                                                        :selectPosition="field.selectPosition"
                                                    />
                                                </div>
                                            </AccordionContent>
                                        </Card>
                                    </AccordionItem>
                                </Accordion>
                            </CardContent>
                            <CardFooter class="bg-stone mb-0 flex items-center justify-end gap-2 rounded-b-xl px-4 py-3">
                                <Button
                                    variant="outline"
                                    @click="goToPreviousTab"
                                    type="button"
                                    class="bg-card text-muted-foreground flex items-center gap-2"
                                >
                                    <FaIcon name="chevron-left" />
                                    Back
                                </Button>
                                <Button variant="outline" @click="goToNextTab" type="button" class="bg-cobalt flex items-center gap-2 text-white">
                                    Next
                                    <FaIcon name="chevron-right" />
                                </Button>
                            </CardFooter>
                        </template>
                        <template #document>
                            <CardContent class="py-4">
                                <div class="flex gap-4">
                                    <!-- Left section (60%) -->
                                    <div class="flex w-[75%] flex-col gap-2">
                                        <Card class="h-[420px] w-full rounded-lg py-2">
                                            <CardContent class="px-2">
                                                <TabsWrapper v-model="activeDocTab" :tabs="docItems">
                                                    <template #all>
                                                        <CardContent class="px-0">
                                                            <div
                                                                v-if="categorizedFiles['all'].length == 0"
                                                                class="flex h-[380px] items-center justify-center"
                                                            >
                                                                <p>No Documents</p>
                                                            </div>
                                                            <div
                                                                v-else
                                                                class="grid grid-cols-2 gap-2 py-2 text-center sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5"
                                                            >
                                                                <div
                                                                    v-for="(doc, index) in categorizedFiles['all']"
                                                                    :key="doc.id"
                                                                    @click="toggleSelect(doc.id)"
                                                                    :class="[
                                                                        'relative flex cursor-pointer flex-col items-center justify-center rounded border p-2 transition',
                                                                        selectedDocIds.includes(doc.id)
                                                                            ? 'border-blue-500 bg-blue-50'
                                                                            : 'border-transparent hover:border-gray-300',
                                                                    ]"
                                                                >
                                                                    <div
                                                                        v-if="selectedDocIds.includes(doc.id)"
                                                                        class="bg-azure absolute top-1 right-1 flex h-5 w-5 items-center justify-center rounded-none text-white"
                                                                    >
                                                                        <span class="text-xs">
                                                                            <FaIcon name="check" class="text-white" />
                                                                        </span>
                                                                    </div>

                                                                    <!-- Thumbnail -->
                                                                    <template v-if="isImage(doc.name)">
                                                                        <img :src="doc.url" alt="preview" class="h-[85px] w-[150px] object-contain" />
                                                                    </template>
                                                                    <template v-else>
                                                                        <span class="text-[35px]">
                                                                            <FaIcon
                                                                                name="file-pdf"
                                                                                class="h-[85px] w-[150px] object-contain text-blue-500"
                                                                            />
                                                                        </span>
                                                                    </template>

                                                                    <!-- File name -->
                                                                    <a
                                                                        :href="doc.url"
                                                                        target="_blank"
                                                                        class="max-w-[100px] truncate text-sm text-blue-700 hover:underline"
                                                                    >
                                                                        {{ doc.name }}
                                                                    </a>
                                                                </div>
                                                            </div>
                                                        </CardContent>
                                                    </template>

                                                    <template #customer-doc>
                                                        <CardContent class="px-0">
                                                            <div
                                                                v-if="categorizedFiles['customer-doc'].length == 0"
                                                                class="flex h-[380px] items-center justify-center"
                                                            >
                                                                <p>No Documents</p>
                                                            </div>
                                                            <div
                                                                v-else
                                                                class="grid grid-cols-2 gap-2 py-2 text-center sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5"
                                                            >
                                                                <div
                                                                    v-for="(doc, index) in categorizedFiles['customer-doc']"
                                                                    :key="doc.id"
                                                                    @click="toggleSelect(doc.id)"
                                                                    :class="[
                                                                        'relative flex cursor-pointer flex-col items-center justify-center rounded border p-2 transition',
                                                                        selectedDocIds.includes(doc.id)
                                                                            ? 'border-blue-500 bg-blue-50'
                                                                            : 'border-transparent hover:border-gray-300',
                                                                    ]"
                                                                >
                                                                    <div
                                                                        v-if="selectedDocIds.includes(doc.id)"
                                                                        class="bg-azure absolute top-1 right-1 flex h-5 w-5 items-center justify-center rounded-none text-white"
                                                                    >
                                                                        <span class="text-xs">
                                                                            <FaIcon name="check" class="text-white" />
                                                                        </span>
                                                                    </div>

                                                                    <!-- Thumbnail -->
                                                                    <template v-if="isImage(doc.name)">
                                                                        <img :src="doc.url" alt="preview" class="h-[85px] w-[150px] object-contain" />
                                                                    </template>
                                                                    <template v-else>
                                                                        <span class="text-[35px]">
                                                                            <FaIcon
                                                                                name="file-pdf"
                                                                                class="h-[85px] w-[150px] object-contain text-blue-500"
                                                                            />
                                                                        </span>
                                                                    </template>

                                                                    <!-- File name -->
                                                                    <a
                                                                        :href="doc.url"
                                                                        target="_blank"
                                                                        class="max-w-[100px] truncate text-sm text-blue-700 hover:underline"
                                                                    >
                                                                        {{ doc.name }}
                                                                    </a>
                                                                </div>
                                                            </div>
                                                        </CardContent>
                                                    </template>

                                                    <template #collateral-doc>
                                                        <CardContent class="px-0">
                                                            <div
                                                                v-if="categorizedFiles['collateral-doc'].length == 0"
                                                                class="flex h-[380px] items-center justify-center"
                                                            >
                                                                <p>No Documents</p>
                                                            </div>
                                                            <div
                                                                v-else
                                                                class="grid grid-cols-2 gap-2 py-2 text-center sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5"
                                                            >
                                                                <div
                                                                    v-for="(doc, index) in categorizedFiles['collateral-doc']"
                                                                    :key="doc.id"
                                                                    @click="toggleSelect(doc.id)"
                                                                    :class="[
                                                                        'relative flex cursor-pointer flex-col items-center justify-center rounded border p-2 transition',
                                                                        selectedDocIds.includes(doc.id)
                                                                            ? 'border-blue-500 bg-blue-50'
                                                                            : 'border-transparent hover:border-gray-300',
                                                                    ]"
                                                                >
                                                                    <div
                                                                        v-if="selectedDocIds.includes(doc.id)"
                                                                        class="bg-azure absolute top-1 right-1 flex h-5 w-5 items-center justify-center rounded-none text-white"
                                                                    >
                                                                        <span class="text-xs">
                                                                            <FaIcon name="check" class="text-white" />
                                                                        </span>
                                                                    </div>

                                                                    <!-- Thumbnail -->
                                                                    <template v-if="isImage(doc.name)">
                                                                        <img :src="doc.url" alt="preview" class="h-[85px] w-[150px] object-contain" />
                                                                    </template>
                                                                    <template v-else>
                                                                        <span class="text-[35px]">
                                                                            <FaIcon
                                                                                name="file-pdf"
                                                                                class="h-[85px] w-[150px] object-contain text-blue-500"
                                                                            />
                                                                        </span>
                                                                    </template>

                                                                    <!-- File name -->
                                                                    <a
                                                                        :href="doc.url"
                                                                        target="_blank"
                                                                        class="max-w-[100px] truncate text-sm text-blue-700 hover:underline"
                                                                    >
                                                                        {{ doc.name }}
                                                                    </a>
                                                                </div>
                                                            </div>
                                                        </CardContent>
                                                    </template>

                                                    <template #security-doc>
                                                        <CardContent class="px-0">
                                                            <div
                                                                v-if="categorizedFiles['security-doc'].length == 0"
                                                                class="flex h-[380px] items-center justify-center"
                                                            >
                                                                <p>No Documents</p>
                                                            </div>
                                                            <div
                                                                v-else
                                                                class="grid grid-cols-2 gap-2 py-2 text-center sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5"
                                                            >
                                                                <div
                                                                    v-for="(doc, index) in categorizedFiles['security-doc']"
                                                                    :key="doc.id"
                                                                    @click="toggleSelect(doc.id)"
                                                                    :class="[
                                                                        'relative flex cursor-pointer flex-col items-center justify-center rounded border p-2 transition',
                                                                        selectedDocIds.includes(doc.id)
                                                                            ? 'border-blue-500 bg-blue-50'
                                                                            : 'border-transparent hover:border-gray-300',
                                                                    ]"
                                                                >
                                                                    <div
                                                                        v-if="selectedDocIds.includes(doc.id)"
                                                                        class="bg-azure absolute top-1 right-1 flex h-5 w-5 items-center justify-center rounded-none text-white"
                                                                    >
                                                                        <span class="text-xs">
                                                                            <FaIcon name="check" class="text-white" />
                                                                        </span>
                                                                    </div>

                                                                    <!-- Thumbnail -->
                                                                    <template v-if="isImage(doc.name)">
                                                                        <img :src="doc.url" alt="preview" class="h-[85px] w-[150px] object-contain" />
                                                                    </template>
                                                                    <template v-else>
                                                                        <span class="text-[35px]">
                                                                            <FaIcon
                                                                                name="file-pdf"
                                                                                class="h-[85px] w-[150px] object-contain text-blue-500"
                                                                            />
                                                                        </span>
                                                                    </template>

                                                                    <!-- File name -->
                                                                    <a
                                                                        :href="doc.url"
                                                                        target="_blank"
                                                                        class="max-w-[100px] truncate text-sm text-blue-700 hover:underline"
                                                                    >
                                                                        {{ doc.name }}
                                                                    </a>
                                                                </div>
                                                            </div>
                                                        </CardContent>
                                                    </template>
                                                </TabsWrapper>
                                            </CardContent>
                                        </Card>
                                    </div>
                                    <div class="w-[25%]">
                                        <!-- TODO: show selected doc info -->
                                        <Card class="flex h-[500px]" v-if="selectedDocIds.length > 0 && selectedFile">
                                            <CardContent>
                                                <div class="mt-4 w-full max-w-md">
                                                    <!-- File name as link -->
                                                    <a
                                                        :href="selectedFile.url"
                                                        target="_blank"
                                                        class="mb-3 block truncate text-sm text-blue-700 hover:underline"
                                                    >
                                                        {{ selectedFile.name }}
                                                    </a>

                                                    <!-- Icon -->
                                                    <div v-if="isImage(selectedFile.name)" class="mb-6 text-center">
                                                        <img
                                                            :src="selectedFile.url"
                                                            alt="preview"
                                                            class="h-auto max-h-[150px] w-full object-contain"
                                                        />
                                                    </div>
                                                    <div v-else class="mb-6 text-center">
                                                        <span class="text-lavender text-[50px]">
                                                            <FaIcon name="file" />
                                                        </span>
                                                    </div>

                                                    <!-- Details -->
                                                    <div class="space-y-4 text-sm">
                                                        <div>
                                                            <p class="font-semibold">File Type:</p>
                                                            <p>{{ selectedFile.type }}</p>
                                                        </div>
                                                        <div>
                                                            <p class="font-semibold">File Size:</p>
                                                            <p>{{ (selectedFile.size / 1024).toFixed(2) }} KB</p>
                                                        </div>
                                                        <div>
                                                            <p class="font-semibold">Created Date:</p>
                                                            <p>{{ selectedFile.createdAt }}</p>
                                                        </div>
                                                        <div>
                                                            <p class="font-semibold">Uploaded By:</p>
                                                            <p>{{ selectedFile.uploadedBy }}</p>
                                                        </div>
                                                    </div>
                                                </div>
                                            </CardContent>
                                        </Card>
                                        <Card v-else class="flex h-[500px] items-center justify-center">
                                            <CardContent>
                                                <div class="text-center">
                                                    <span class="text-lavender text-[50px]">
                                                        <FaIcon name="file" />
                                                    </span>
                                                    <p>0 Item</p>
                                                    <p>Select a single file to get more information</p>
                                                </div>
                                            </CardContent>
                                        </Card>
                                    </div>
                                </div>
                            </CardContent>
                            <CardFooter class="bg-stone flex justify-end gap-2 px-4 py-3">
                                <Button
                                    variant="outline"
                                    @click="goToPreviousTab"
                                    type="button"
                                    class="bg-card text-muted-foreground flex items-center gap-2"
                                >
                                    <FaIcon name="chevron-left" />
                                    Back
                                </Button>
                                <Button type="button" @click="submit(true)" class="bg-teal flex items-center gap-2 text-white">
                                    Save as Draft
                                    <FaIcon name="floppy-disk" />
                                </Button>
                                <Button type="button" @click="submit(false)" class="bg-green flex items-center gap-2 text-white">
                                    Submit
                                    <FaIcon name="paper-plane" />
                                </Button>
                            </CardFooter>
                        </template>
                    </TabsWrapper>
                </Card>
            </form>
        </div>
    </AppLayout>
</template>
