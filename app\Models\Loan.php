<?php

namespace App\Models;

use App\Enums\Loan\LoanStatus;
use App\Traits\GeneratesLoanTransactions;
use App\Traits\UniqueCodeTrait;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;

/**
 * Loan model for managing loan information
 */
class Loan extends BaseModel
{
    use GeneratesLoanTransactions, HasFactory, UniqueCodeTrait;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<string>
     */
    protected $fillable = [
        'uuid',
        'code',
        'customer_id',
        'customer',
        'company_id',
        'company',
        'team_id',
        'team',
        'agent_id',
        'agent',
        'selection_type_id',
        'type',
        'status',
        'remark',
        'created_by',
        'updated_by',
        'deleted_by',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'uuid' => 'string',
            'customer_id' => 'integer',
            'company_id' => 'integer',
            'team_id' => 'integer',
            'agent_id' => 'integer',
            'selection_type_id' => 'integer',
            'status' => LoanStatus::class,
            'deleted_at' => 'datetime',
            'created_by' => 'integer',
            'updated_by' => 'integer',
            'deleted_by' => 'integer',
        ];
    }

    /**
     * Get the client (customer) associated with this loan.
     * Note: The migration references 'client_profiles' but the actual table is 'customer_profiles'
     */
    public function client(): BelongsTo
    {
        return $this->belongsTo(CustomerProfile::class, 'customer_id');
    }

    /**
     * Get the company associated with this loan.
     */
    public function company(): BelongsTo
    {
        return $this->belongsTo(Company::class, 'company_id');
    }

    /**
     * Get the team associated with this loan.
     */
    public function team(): BelongsTo
    {
        return $this->belongsTo(Team::class, 'team_id');
    }

    /**
     * Get the agent (user) associated with this loan.
     */
    public function agent(): BelongsTo
    {
        return $this->belongsTo(AgentProfile::class, 'agent_id');
    }

    /**
     * Get the selection type associated with this loan.
     */
    public function selectionType(): BelongsTo
    {
        return $this->belongsTo(Selection::class, 'selection_type_id');
    }

    /**
     * Get the loan customer profiles for this loan.
     */
    public function loanCustomerProfiles(): HasMany
    {
        return $this->hasMany(LoanCustomerProfile::class, 'loan_id');
    }

    /**
     * Get the loan detail for this loan.
     */
    public function loanDetail(): HasOne
    {
        return $this->hasOne(LoanDetail::class, 'loan_id');
    }

    /**
     * Get the loan documents for this loan.
     */
    public function loanDocuments(): HasMany
    {
        return $this->hasMany(LoanDocument::class, 'loan_id');
    }

    /**
     * Get the loan emergencies for this loan.
     */
    public function loanEmergencies(): HasMany
    {
        return $this->hasMany(LoanEmergency::class, 'loan_id');
    }

    /**
     * Get the loan guarantors for this loan.
     */
    public function loanGuarantors(): HasMany
    {
        return $this->hasMany(LoanGuarantor::class, 'loan_id');
    }

    /**
     * Get the loan customer collaterals for this loan.
     */
    public function loanCustomerCollaterals(): HasMany
    {
        return $this->hasMany(LoanCustomerCollateral::class, 'loan_id');
    }

    /**
     * Get the loan bank accounts for this loan.
     */
    public function loanBankAccounts(): HasMany
    {
        return $this->hasMany(LoanBankAccount::class, 'loan_id');
    }

    /**
     * Get the loan installments for this loan.
     */
    public function installments(): HasMany
    {
        return $this->hasMany(LoanInstallment::class, 'loan_id');
    }

    /**
     * Get the loan transactions for this loan.
     */
    public function loanTxns(): HasMany
    {
        return $this->hasMany(LoanTxn::class, 'loan_id');
    }

    /**
     * The "booting" method of the model.
     */
    protected static function boot()
    {
        parent::boot();

        // Auto-generate code if not provided
        static::creating(function ($model) {
            if (empty($model->code)) {
                $model->code = self::generateUniqueCode();
            }
        });
    }

    /**
     * Generate a unique code for a new loan.
     */
    public static function generateUniqueCode(): string
    {
        return self::generateUniqueCodeWithPrefix('LN');
    }

    /**
     * Scope a query to get loans for dropdown lists.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeForDropdown($query)
    {
        return $query->select('id', 'code', 'client', 'company', 'status')
            ->orderBy('code');
    }

    /**
     * Generate a late interest transaction for this loan
     */
    public function addLateInterestCharge(int $installmentId, int $tenure, float $amount, ?string $remark = null): LoanTxn
    {
        return $this->generateLateInterestTransaction($this->id, $installmentId, $tenure, $amount, $remark);
    }

    /**
     * Generate a legal fee transaction for this loan
     */
    public function addLegalFeeCharge(int $installmentId, int $tenure, float $amount, ?string $remark = null): LoanTxn
    {
        return $this->generateLegalFeeTransaction($this->id, $installmentId, $tenure, $amount, $remark);
    }

    /**
     * Generate a miscellaneous charge transaction for this loan
     */
    public function addMiscCharge(int $installmentId, int $tenure, float $amount, ?string $remark = null): LoanTxn
    {
        return $this->generateMiscChargeTransaction($this->id, $installmentId, $tenure, $amount, $remark);
    }

    /**
     * Generate a postage transaction for this loan
     */
    public function addPostageCharge(int $installmentId, int $tenure, float $amount, ?string $remark = null): LoanTxn
    {
        return $this->generatePostageTransaction($this->id, $installmentId, $tenure, $amount, $remark);
    }
}
