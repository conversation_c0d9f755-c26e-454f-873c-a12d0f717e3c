<?php

namespace App\Http\Resources\Loans;

use App\Enums\Loan\LoanTxnStatus;
use App\Enums\Loan\LoanTxnType;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * LoanTransactionResource
 *
 * Transforms LoanTransaction model data for API responses.
 */
class LoanTransactionResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'uuid' => $this->uuid,
            'code' => $this->code,
            'tenure' => $this->tenure,
            'current_no' => $this->current_no,
            'txn_date' => $this->txn_date,
            'txn_type' => LoanTxnType::from($this->loan_txn_type_id)->label(),
            'amount' => number_format($this->amount ?? 0, 2),
            'payment_amount' => number_format($this->payment_amount ?? 0, 2),
            'balance_amount' => number_format($this->amount ?? 0, 2),
            'status' => $this->status,
            'is_overdue' => $this->due_date < now() && $this->status === LoanTxnStatus::UNPAID->value,
            'days_overdue' => $this->due_date < now() && $this->status === LoanTxnStatus::UNPAID->value
                ? now()->diffInDays($this->due_date)
                : 0,
            'remark' => $this->remark,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
            'created_by' => $this->whenLoaded('createdBy', fn () => [
                'id' => $this->createdBy->id,
                'name' => $this->createdBy->username,
            ]),
            'updated_by' => $this->whenLoaded('updatedBy', fn () => [
                'id' => $this->updatedBy->id,
                'name' => $this->updatedBy->username,
            ]),
        ];
    }
}
