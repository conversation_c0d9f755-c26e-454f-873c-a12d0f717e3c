<?php

namespace App\Models;

use App\Enums\Loan\LoanTxnStatus;
use App\Enums\Loan\LoanTxnType;
use App\Traits\UniqueCodeTrait;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * LoanTxn model for managing loan transactions
 */
class LoanTxn extends BaseModel
{
    use HasFactory, UniqueCodeTrait;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<string>
     */
    protected $fillable = [
        'uuid',
        'code',
        'loan_id',
        'loan_installment_id',
        'loan_txn_type_id',
        'txn_date',
        'tenure',
        'txn_type',
        'amount',
        'status',
        'remark',
        'sort_date',
        'created_by',
        'updated_by',
        'deleted_by',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'uuid' => 'string',
            'loan_id' => 'integer',
            'loan_installment_id' => 'integer',
            'loan_txn_type_id' => 'integer',
            'txn_date' => 'datetime',
            'tenure' => 'integer',
            'amount' => 'decimal:2',
            'status' => LoanTxnStatus::class,
            'sort_date' => 'datetime',
            'deleted_at' => 'datetime',
            'created_by' => 'integer',
            'updated_by' => 'integer',
            'deleted_by' => 'integer',
        ];
    }

    public function getTxnDateAttribute($value): ?string
    {
        return $value ? $this->formatDateTime($this->convertToTimezone($value), 'Y-m-d') : null;
    }

    public function scopeWithLoanId($query, $loanId)
    {
        return $query->where('loan_id', $loanId);
    }

    /**
     * Get the loan that owns this transaction.
     */
    public function loan(): BelongsTo
    {
        return $this->belongsTo(Loan::class);
    }

    /**
     * Get the loan installment that owns this transaction.
     */
    public function loanInstallment(): BelongsTo
    {
        return $this->belongsTo(LoanInstallment::class);
    }

    /**
     * Get the loan transaction type that owns this transaction.
     */
    public function loanTxnType(): BelongsTo
    {
        return $this->belongsTo(LoanTxnType::class);
    }

    /**
     * Get the transaction details for this transaction.
     */
    public function loanTxnDetails(): HasMany
    {
        return $this->hasMany(LoanTxnDetail::class);
    }

    /**
     * The "booting" method of the model.
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($model) {
            if (empty($model->code)) {
                $model->code = self::generateUniqueCode();
            }
        });
    }

    /**
     * Generate a unique code for a new loan transaction.
     */
    public static function generateUniqueCode(): string
    {
        return self::generateUniqueCodeWithPrefix('LTX');
    }

    /**
     * Scope a query to get transactions for dropdown lists.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeForDropdown($query)
    {
        return $query->select('id', 'code', 'amount', 'status')
            ->orderBy('code');
    }

    public function scopeUnpaid($q)
    {
        return $q->where('status', LoanTxnStatus::UNPAID->value);
    }

    public function scopeLateInterest($q)
    {
        return $q->where('loan_txn_type_id', LoanTxnType::LATE_INTEREST->value);
    }

    public function scopeLegalFee($q)
    {
        return $q->where('loan_txn_type_id', LoanTxnType::LEGAL_FEE->value);
    }

    public function scopePostage($q)
    {
        return $q->where('loan_txn_type_id', LoanTxnType::POSTAGE->value);
    }

    public function scopeMiscCharge($q)
    {
        return $q->where('loan_txn_type_id', LoanTxnType::MISC_CHARGE->value);
    }

    public function scopeInstallment($q)
    {
        return $q->where('loan_txn_type_id', LoanTxnType::INSTALLMENT->value);
    }

    /**
     * Scope to get installments by status
     */
    public function scopeByStatus($query, LoanTxnStatus $status)
    {
        return $query->where('status', $status);
    }

    /**
     * Scope to get transactions by loan ID
     */
    public function scopeOrderedByTypeAndDate($query)
    {
        return $query
            ->leftJoin('loan_txn_types', 'loan_txns.loan_txn_type_id', '=', 'loan_txn_types.id')
            ->selectRaw('loan_txns.*, ROW_NUMBER() OVER (ORDER BY loan_txn_types.sort_order, loan_txns.sort_date, loan_txns.id) as current_no')
            ->orderBy('loan_txn_types.sort_order')
            ->orderBy('loan_txns.sort_date')
            ->orderBy('loan_txns.id');
    }

    /**
     * Get the total balance amount for a loan, including all transaction types.
     *
     * @return array<string, string>
     */
    public static function getBalanceTransaction(int $loanId, ?int $currentTenure = null): array
    {
        $charges = [
            'installment_owe_amount' => 0.0,
            'late_interest_charges_amount' => 0.0,
            'postage_charges_amount' => 0.0,
            'legal_charges_amount' => 0.0,
            'misc_charges_amount' => 0.0,
        ];

        if (! is_null($currentTenure)) {
            $installmentOwe = self::withLoanId($loanId)
                ->unpaid()
                ->installment()
                ->where('tenure', '<=', $currentTenure)
                ->selectRaw('SUM(amount) as installment_owe_amount')
                ->value('installment_owe_amount');

            $charges['installment_owe_amount'] = (float) ($installmentOwe ?? 0);

            $lateInterestCharges = self::withLoanId($loanId)
                ->lateInterest()
                ->unpaid()
                ->selectRaw('SUM(amount) as late_interest_charges_amount')
                ->value('late_interest_charges_amount');

            $charges['late_interest_charges_amount'] = (float) ($lateInterestCharges ?? 0);

            $miscCharges = self::withLoanId($loanId)
                ->miscCharge()
                ->unpaid()
                ->selectRaw('SUM(amount) as misc_charges_amount')
                ->value('misc_charges_amount');
            $charges['misc_charges_amount'] = (float) ($miscCharges ?? 0);

            $legalCharges = self::withLoanId($loanId)
                ->legalFee()
                ->unpaid()
                ->selectRaw('SUM(amount) as legal_charges_amount')
                ->value('legal_charges_amount');
            $charges['legal_charges_amount'] = (float) ($legalCharges ?? 0);

            $postageCharges = self::withLoanId($loanId)
                ->postage()
                ->unpaid()
                ->selectRaw('SUM(amount) as postage_charges_amount')
                ->value('postage_charges_amount');

            $charges['postage_charges_amount'] = (float) ($postageCharges ?? 0);
        }

        $totalBalance = array_sum($charges);

        return collect([
            'total_balance_amount' => $totalBalance,
        ] + $charges)->map(fn ($value) => number_format((float) $value, 2))->toArray();
    }
}
